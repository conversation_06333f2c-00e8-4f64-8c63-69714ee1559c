import 'dart:math';

enum MarketCondition { 
  recession, 
  slow, 
  normal, 
  growth, 
  boom 
}

class Market {
  double inflationRate;
  MarketCondition condition;
  Map<String, double> industryTrends;
  List<MarketEvent> activeEvents;
  DateTime lastUpdate;
  
  Market({
    this.inflationRate = 0.02,
    this.condition = MarketCondition.normal,
    Map<String, double>? industryTrends,
    List<MarketEvent>? activeEvents,
    DateTime? lastUpdate,
  }) : 
    industryTrends = industryTrends ?? _getDefaultTrends(),
    activeEvents = activeEvents ?? [],
    lastUpdate = lastUpdate ?? DateTime.now();

  static Map<String, double> _getDefaultTrends() {
    return {
      'technology': 1.0,
      'retail': 1.0,
      'manufacturing': 1.0,
      'food': 1.0,
      'healthcare': 1.0,
    };
  }

  // Market condition effects
  double get demandMultiplier {
    switch (condition) {
      case MarketCondition.recession:
        return 0.6;
      case MarketCondition.slow:
        return 0.8;
      case MarketCondition.normal:
        return 1.0;
      case MarketCondition.growth:
        return 1.3;
      case MarketCondition.boom:
        return 1.6;
    }
  }

  double get costMultiplier {
    switch (condition) {
      case MarketCondition.recession:
        return 0.9; // Lower costs during recession
      case MarketCondition.slow:
        return 0.95;
      case MarketCondition.normal:
        return 1.0;
      case MarketCondition.growth:
        return 1.05;
      case MarketCondition.boom:
        return 1.15; // Higher costs during boom
    }
  }

  String get conditionName {
    switch (condition) {
      case MarketCondition.recession:
        return 'Recession';
      case MarketCondition.slow:
        return 'Slow Growth';
      case MarketCondition.normal:
        return 'Normal';
      case MarketCondition.growth:
        return 'Growth';
      case MarketCondition.boom:
        return 'Economic Boom';
    }
  }

  String get conditionDescription {
    switch (condition) {
      case MarketCondition.recession:
        return 'Demand is low, but costs are reduced';
      case MarketCondition.slow:
        return 'Modest demand with stable costs';
      case MarketCondition.normal:
        return 'Balanced market conditions';
      case MarketCondition.growth:
        return 'Increased demand with rising costs';
      case MarketCondition.boom:
        return 'High demand but expensive operations';
    }
  }

  // Market simulation
  void updateMarket() {
    final now = DateTime.now();
    final daysSinceUpdate = now.difference(lastUpdate).inDays;
    
    if (daysSinceUpdate >= 7) { // Weekly updates
      _updateMarketCondition();
      _updateIndustryTrends();
      _updateInflation();
      _processEvents();
      _generateRandomEvents();
      lastUpdate = now;
    }
  }

  void _updateMarketCondition() {
    final random = Random();
    final change = random.nextInt(100);
    
    // 70% chance to stay the same, 30% chance to change
    if (change < 70) return;
    
    final currentIndex = MarketCondition.values.indexOf(condition);
    final direction = random.nextBool() ? 1 : -1;
    final newIndex = (currentIndex + direction).clamp(0, MarketCondition.values.length - 1);
    
    condition = MarketCondition.values[newIndex];
  }

  void _updateIndustryTrends() {
    final random = Random();
    
    for (final industry in industryTrends.keys) {
      final change = (random.nextDouble() - 0.5) * 0.2; // ±10% change
      industryTrends[industry] = (industryTrends[industry]! + change).clamp(0.5, 2.0);
    }
  }

  void _updateInflation() {
    final random = Random();
    final change = (random.nextDouble() - 0.5) * 0.01; // ±0.5% change
    inflationRate = (inflationRate + change).clamp(-0.05, 0.10);
  }

  void _processEvents() {
    activeEvents.removeWhere((event) => event.isExpired);
  }

  void _generateRandomEvents() {
    final random = Random();
    if (random.nextInt(100) < 20) { // 20% chance of new event
      activeEvents.add(MarketEvent.generateRandom());
    }
  }

  // Industry-specific multipliers
  double getIndustryMultiplier(String industry) {
    return industryTrends[industry] ?? 1.0;
  }

  // Serialization
  Map<String, dynamic> toMap() {
    return {
      'inflationRate': inflationRate,
      'condition': condition.toString(),
      'industryTrends': industryTrends,
      'lastUpdate': lastUpdate.millisecondsSinceEpoch,
    };
  }

  factory Market.fromMap(Map<String, dynamic> map) {
    return Market(
      inflationRate: map['inflationRate']?.toDouble() ?? 0.02,
      condition: MarketCondition.values.firstWhere(
        (e) => e.toString() == map['condition'],
        orElse: () => MarketCondition.normal,
      ),
      industryTrends: Map<String, double>.from(map['industryTrends'] ?? _getDefaultTrends()),
      lastUpdate: DateTime.fromMillisecondsSinceEpoch(map['lastUpdate']),
    );
  }
}

class MarketEvent {
  final String id;
  final String title;
  final String description;
  final Map<String, double> effects;
  final DateTime startDate;
  final int durationDays;
  
  MarketEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.effects,
    required this.startDate,
    required this.durationDays,
  });

  bool get isExpired {
    return DateTime.now().difference(startDate).inDays > durationDays;
  }

  static MarketEvent generateRandom() {
    final events = [
      MarketEvent(
        id: 'tech_boom',
        title: 'Tech Boom',
        description: 'Technology sector is experiencing rapid growth!',
        effects: {'technology': 1.5},
        startDate: DateTime.now(),
        durationDays: 30,
      ),
      MarketEvent(
        id: 'supply_shortage',
        title: 'Supply Chain Issues',
        description: 'Manufacturing costs have increased due to supply shortages.',
        effects: {'manufacturing': 0.7},
        startDate: DateTime.now(),
        durationDays: 14,
      ),
      MarketEvent(
        id: 'health_crisis',
        title: 'Health Crisis',
        description: 'Healthcare demand has surged dramatically.',
        effects: {'healthcare': 2.0},
        startDate: DateTime.now(),
        durationDays: 60,
      ),
      MarketEvent(
        id: 'retail_slump',
        title: 'Retail Slowdown',
        description: 'Consumer spending on retail goods has decreased.',
        effects: {'retail': 0.8},
        startDate: DateTime.now(),
        durationDays: 21,
      ),
    ];
    
    final random = Random();
    return events[random.nextInt(events.length)];
  }
}
