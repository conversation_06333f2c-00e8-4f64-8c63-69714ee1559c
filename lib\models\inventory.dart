import 'item.dart';

class InventorySlot {
  final Item item;
  final int quantity;

  InventorySlot({
    required this.item,
    this.quantity = 1,
  });

  InventorySlot copyWith({
    Item? item,
    int? quantity,
  }) {
    return InventorySlot(
      item: item ?? this.item,
      quantity: quantity ?? this.quantity,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'itemId': item.id,
      'quantity': quantity,
    };
  }

  factory InventorySlot.fromMap(Map<String, dynamic> map) {
    final item = ItemDatabase.getItemById(map['itemId']);
    if (item == null) {
      throw Exception('Item not found: ${map['itemId']}');
    }
    
    return InventorySlot(
      item: item,
      quantity: map['quantity'] ?? 1,
    );
  }

  @override
  String toString() {
    return 'InventorySlot(item: ${item.name}, quantity: $quantity)';
  }
}

class Inventory {
  final List<InventorySlot> slots;
  final int maxSlots;

  Inventory({
    List<InventorySlot>? slots,
    this.maxSlots = 20,
  }) : slots = slots ?? [];

  // Add item to inventory
  Inventory addItem(Item item, {int quantity = 1}) {
    final newSlots = List<InventorySlot>.from(slots);
    
    // Check if item already exists (for stackable items)
    if (item.consumable) {
      final existingIndex = newSlots.indexWhere((slot) => slot.item.id == item.id);
      if (existingIndex != -1) {
        // Stack with existing item
        newSlots[existingIndex] = newSlots[existingIndex].copyWith(
          quantity: newSlots[existingIndex].quantity + quantity,
        );
        return Inventory(slots: newSlots, maxSlots: maxSlots);
      }
    }
    
    // Add as new slot if there's space
    if (newSlots.length < maxSlots) {
      newSlots.add(InventorySlot(item: item, quantity: quantity));
      return Inventory(slots: newSlots, maxSlots: maxSlots);
    }
    
    // Inventory full
    return this;
  }

  // Remove item from inventory
  Inventory removeItem(String itemId, {int quantity = 1}) {
    final newSlots = List<InventorySlot>.from(slots);
    final index = newSlots.indexWhere((slot) => slot.item.id == itemId);
    
    if (index != -1) {
      final slot = newSlots[index];
      if (slot.quantity <= quantity) {
        // Remove entire slot
        newSlots.removeAt(index);
      } else {
        // Reduce quantity
        newSlots[index] = slot.copyWith(quantity: slot.quantity - quantity);
      }
    }
    
    return Inventory(slots: newSlots, maxSlots: maxSlots);
  }

  // Get item quantity
  int getItemQuantity(String itemId) {
    final slot = slots.where((slot) => slot.item.id == itemId).firstOrNull;
    return slot?.quantity ?? 0;
  }

  // Check if item exists
  bool hasItem(String itemId, {int quantity = 1}) {
    return getItemQuantity(itemId) >= quantity;
  }

  // Get items by type
  List<InventorySlot> getItemsByType(ItemType type) {
    return slots.where((slot) => slot.item.type == type).toList();
  }

  // Get all weapons
  List<InventorySlot> get weapons => getItemsByType(ItemType.weapon);
  
  // Get all armor
  List<InventorySlot> get armor => getItemsByType(ItemType.armor);
  
  // Get all consumables
  List<InventorySlot> get consumables => getItemsByType(ItemType.consumable);
  
  // Get all accessories
  List<InventorySlot> get accessories => getItemsByType(ItemType.accessory);

  // Calculate total value
  int get totalValue {
    return slots.fold(0, (total, slot) => total + (slot.item.value * slot.quantity));
  }

  // Check if inventory is full
  bool get isFull => slots.length >= maxSlots;

  // Get available slots
  int get availableSlots => maxSlots - slots.length;

  // Sort inventory by type, then by rarity, then by value
  Inventory sort() {
    final sortedSlots = List<InventorySlot>.from(slots);
    sortedSlots.sort((a, b) {
      // First by type
      final typeComparison = a.item.type.index.compareTo(b.item.type.index);
      if (typeComparison != 0) return typeComparison;
      
      // Then by rarity (higher rarity first)
      final rarityComparison = b.item.rarity.index.compareTo(a.item.rarity.index);
      if (rarityComparison != 0) return rarityComparison;
      
      // Finally by value (higher value first)
      return b.item.value.compareTo(a.item.value);
    });
    
    return Inventory(slots: sortedSlots, maxSlots: maxSlots);
  }

  Map<String, dynamic> toMap() {
    return {
      'slots': slots.map((slot) => slot.toMap()).toList(),
      'maxSlots': maxSlots,
    };
  }

  factory Inventory.fromMap(Map<String, dynamic> map) {
    final slotsData = map['slots'] as List<dynamic>? ?? [];
    final slots = slotsData
        .map((slotData) => InventorySlot.fromMap(slotData as Map<String, dynamic>))
        .toList();
    
    return Inventory(
      slots: slots,
      maxSlots: map['maxSlots'] ?? 20,
    );
  }

  @override
  String toString() {
    return 'Inventory(${slots.length}/$maxSlots slots, value: $totalValue gold)';
  }
}

// Extension to add firstOrNull method if not available
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
