import 'dart:math';
import 'character_stats.dart';
import 'item.dart';

enum EnemyType { goblin, orc, skeleton, dragon, boss }

class Enemy {
  final String id;
  final String name;
  final EnemyType type;
  final CharacterStats stats;
  final List<String> abilities;
  final LootTable lootTable;
  final String description;

  Enemy({
    required this.id,
    required this.name,
    required this.type,
    required this.stats,
    this.abilities = const [],
    required this.lootTable,
    this.description = '',
  });

  // AI decision making
  BattleAction chooseAction(CharacterStats playerStats) {
    final random = Random();
    
    // Simple AI logic based on enemy type and situation
    switch (type) {
      case EnemyType.goblin:
        // Goblins are aggressive but simple
        return random.nextDouble() < 0.8 
            ? BattleAction.attack 
            : BattleAction.defend;
            
      case EnemyType.orc:
        // Orcs are very aggressive
        return random.nextDouble() < 0.9 
            ? BattleAction.attack 
            : BattleAction.defend;
            
      case EnemyType.skeleton:
        // Skeletons use abilities more often
        if (abilities.isNotEmpty && random.nextDouble() < 0.4) {
          return BattleAction.ability;
        }
        return random.nextDouble() < 0.7 
            ? BattleAction.attack 
            : BattleAction.defend;
            
      case EnemyType.dragon:
        // Dragons are strategic
        if (stats.currentHp < stats.maxHp * 0.3 && random.nextDouble() < 0.3) {
          return BattleAction.defend; // Defensive when low HP
        }
        if (abilities.isNotEmpty && random.nextDouble() < 0.6) {
          return BattleAction.ability;
        }
        return BattleAction.attack;
        
      case EnemyType.boss:
        // Bosses use complex patterns
        if (abilities.isNotEmpty && random.nextDouble() < 0.5) {
          return BattleAction.ability;
        }
        return random.nextDouble() < 0.8 
            ? BattleAction.attack 
            : BattleAction.defend;
    }
  }

  // Calculate damage dealt
  int calculateDamage({bool isDefending = false}) {
    final random = Random();
    final baseDamage = stats.attack;
    final variance = (baseDamage * 0.2).round(); // ±20% variance
    final damage = baseDamage + random.nextInt(variance * 2) - variance;
    
    return isDefending ? (damage * 0.5).round() : damage;
  }

  // Take damage and return new enemy state
  Enemy takeDamage(int damage) {
    final actualDamage = (damage - stats.defense).clamp(1, damage);
    final newHp = (stats.currentHp - actualDamage).clamp(0, stats.maxHp);
    
    return Enemy(
      id: id,
      name: name,
      type: type,
      stats: stats.copyWith(currentHp: newHp),
      abilities: abilities,
      lootTable: lootTable,
      description: description,
    );
  }

  bool get isAlive => stats.currentHp > 0;

  @override
  String toString() {
    return 'Enemy(name: $name, type: $type, hp: ${stats.currentHp}/${stats.maxHp})';
  }
}

enum BattleAction { attack, defend, ability, item, flee }

class LootTable {
  final int goldMin;
  final int goldMax;
  final int experienceReward;
  final List<LootDrop> possibleItems;

  LootTable({
    required this.goldMin,
    required this.goldMax,
    required this.experienceReward,
    this.possibleItems = const [],
  });

  BattleRewards generateRewards() {
    final random = Random();
    final gold = goldMin + random.nextInt(goldMax - goldMin + 1);
    final items = <Item>[];

    for (final drop in possibleItems) {
      if (random.nextDouble() < drop.dropChance) {
        final item = ItemDatabase.getItemById(drop.itemId);
        if (item != null) {
          items.add(item);
        }
      }
    }

    return BattleRewards(
      gold: gold,
      experience: experienceReward,
      items: items,
    );
  }
}

class LootDrop {
  final String itemId;
  final double dropChance; // 0.0 to 1.0

  LootDrop({
    required this.itemId,
    required this.dropChance,
  });
}

class BattleRewards {
  final int gold;
  final int experience;
  final List<Item> items;

  BattleRewards({
    required this.gold,
    required this.experience,
    this.items = const [],
  });

  @override
  String toString() {
    return 'BattleRewards(gold: $gold, exp: $experience, items: ${items.length})';
  }
}

// Predefined enemies
class EnemyDatabase {
  static final List<Enemy> enemies = [
    Enemy(
      id: 'goblin',
      name: 'Goblin',
      type: EnemyType.goblin,
      stats: CharacterStats(
        level: 1,
        maxHp: 30,
        currentHp: 30,
        attack: 8,
        defense: 2,
        speed: 6,
      ),
      lootTable: LootTable(
        goldMin: 5,
        goldMax: 15,
        experienceReward: 25,
        possibleItems: [
          LootDrop(itemId: 'health_potion', dropChance: 0.3),
          LootDrop(itemId: 'rusty_sword', dropChance: 0.1),
        ],
      ),
      description: 'A small, green-skinned creature with sharp teeth.',
    ),
    
    Enemy(
      id: 'orc',
      name: 'Orc Warrior',
      type: EnemyType.orc,
      stats: CharacterStats(
        level: 3,
        maxHp: 60,
        currentHp: 60,
        attack: 15,
        defense: 5,
        speed: 4,
      ),
      abilities: ['rage'],
      lootTable: LootTable(
        goldMin: 15,
        goldMax: 30,
        experienceReward: 50,
        possibleItems: [
          LootDrop(itemId: 'iron_sword', dropChance: 0.2),
          LootDrop(itemId: 'leather_armor', dropChance: 0.25),
          LootDrop(itemId: 'health_potion', dropChance: 0.4),
        ],
      ),
      description: 'A brutish warrior with massive muscles and a bad temper.',
    ),
    
    Enemy(
      id: 'skeleton',
      name: 'Skeleton Mage',
      type: EnemyType.skeleton,
      stats: CharacterStats(
        level: 4,
        maxHp: 45,
        currentHp: 45,
        attack: 18,
        defense: 3,
        speed: 7,
      ),
      abilities: ['bone_spear', 'dark_magic'],
      lootTable: LootTable(
        goldMin: 20,
        goldMax: 40,
        experienceReward: 75,
        possibleItems: [
          LootDrop(itemId: 'greater_health_potion', dropChance: 0.3),
          LootDrop(itemId: 'speed_ring', dropChance: 0.15),
          LootDrop(itemId: 'strength_potion', dropChance: 0.2),
        ],
      ),
      description: 'An undead spellcaster wreathed in dark energy.',
    ),
    
    Enemy(
      id: 'dragon',
      name: 'Young Dragon',
      type: EnemyType.dragon,
      stats: CharacterStats(
        level: 8,
        maxHp: 150,
        currentHp: 150,
        attack: 30,
        defense: 12,
        speed: 10,
      ),
      abilities: ['fire_breath', 'wing_attack', 'intimidate'],
      lootTable: LootTable(
        goldMin: 100,
        goldMax: 200,
        experienceReward: 200,
        possibleItems: [
          LootDrop(itemId: 'dragon_blade', dropChance: 0.1),
          LootDrop(itemId: 'dragon_scale_armor', dropChance: 0.08),
          LootDrop(itemId: 'power_amulet', dropChance: 0.15),
          LootDrop(itemId: 'greater_health_potion', dropChance: 0.6),
        ],
      ),
      description: 'A fearsome dragon with scales that gleam like metal.',
    ),
  ];

  static Enemy? getEnemyById(String id) {
    try {
      return enemies.firstWhere((enemy) => enemy.id == id);
    } catch (e) {
      return null;
    }
  }

  static Enemy getRandomEnemy({int? maxLevel}) {
    final random = Random();
    var availableEnemies = enemies;
    
    if (maxLevel != null) {
      availableEnemies = enemies.where((e) => e.stats.level <= maxLevel).toList();
    }
    
    if (availableEnemies.isEmpty) {
      availableEnemies = [enemies.first]; // Fallback to first enemy
    }
    
    return availableEnemies[random.nextInt(availableEnemies.length)];
  }

  static List<Enemy> getEnemiesForLevel(int playerLevel) {
    // Return enemies appropriate for player level
    final minLevel = (playerLevel - 2).clamp(1, 10);
    final maxLevel = playerLevel + 2;
    
    return enemies.where((e) => 
      e.stats.level >= minLevel && e.stats.level <= maxLevel
    ).toList();
  }
}
