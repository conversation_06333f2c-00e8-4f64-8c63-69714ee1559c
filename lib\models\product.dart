enum ProductCategory {
  software,
  hardware,
  service,
  food,
  clothing,
  electronics
}

class Product {
  final String id;
  final String name;
  final ProductCategory category;
  double productionCost;
  double sellingPrice;
  int stock;
  double quality;
  double demand;
  int unitsSold;
  DateTime lastRestocked;

  Product({
    required this.id,
    required this.name,
    required this.category,
    required this.productionCost,
    required this.sellingPrice,
    this.stock = 0,
    this.quality = 1.0,
    this.demand = 1.0,
    this.unitsSold = 0,
    DateTime? lastRestocked,
  }) : lastRestocked = lastRestocked ?? DateTime.now();

  // Profit calculations
  double get profitPerUnit => sellingPrice - productionCost;
  double get totalProfit => profitPerUnit * unitsSold;
  double get profitMargin => profitPerUnit / sellingPrice;

  // Market dynamics
  double get effectiveDemand {
    double baseDemand = demand;
    double qualityMultiplier = quality;
    double priceMultiplier = _getPriceMultiplier();
    return baseDemand * qualityMultiplier * priceMultiplier;
  }

  double _getPriceMultiplier() {
    // Lower prices increase demand, higher prices decrease it
    double optimalPrice = productionCost * 2.0; // 100% markup is optimal
    double priceRatio = sellingPrice / optimalPrice;

    if (priceRatio <= 0.5) return 1.5; // Very cheap = high demand
    if (priceRatio <= 1.0) return 1.2; // Reasonable price = good demand
    if (priceRatio <= 1.5) return 1.0; // Normal demand
    if (priceRatio <= 2.0) return 0.7; // Expensive = lower demand
    return 0.4; // Very expensive = very low demand
  }

  // Sales simulation
  int simulateSales(int salesForce, double marketConditions) {
    if (stock <= 0) return 0;

    double baseSales = effectiveDemand * salesForce * marketConditions;
    int potentialSales = (baseSales * (0.8 + (DateTime.now().millisecond % 40) / 100)).round();
    int actualSales = potentialSales > stock ? stock : potentialSales;

    return actualSales;
  }

  // Inventory management
  void restock(int quantity, double totalCost) {
    stock += quantity;
    lastRestocked = DateTime.now();
    // Update production cost based on bulk purchasing
    if (quantity > 100) {
      productionCost *= 0.95; // 5% discount for bulk
    }
  }

  void sell(int quantity) {
    if (quantity <= stock) {
      stock -= quantity;
      unitsSold += quantity;
    }
  }

  void adjustPrice(double newPrice) {
    sellingPrice = newPrice.clamp(productionCost * 0.5, productionCost * 10);
  }

  void improveQuality(double improvement) {
    quality = (quality + improvement).clamp(0.1, 3.0);
  }

  // Market analysis
  String get priceAnalysis {
    double markup = (sellingPrice / productionCost - 1) * 100;
    if (markup < 20) return 'Too Low';
    if (markup < 50) return 'Low';
    if (markup < 100) return 'Good';
    if (markup < 200) return 'High';
    return 'Too High';
  }

  String get stockStatus {
    if (stock == 0) return 'Out of Stock';
    if (stock < 10) return 'Low Stock';
    if (stock < 50) return 'Normal';
    return 'High Stock';
  }

  String get categoryName {
    switch (category) {
      case ProductCategory.software:
        return 'Software';
      case ProductCategory.hardware:
        return 'Hardware';
      case ProductCategory.service:
        return 'Service';
      case ProductCategory.food:
        return 'Food';
      case ProductCategory.clothing:
        return 'Clothing';
      case ProductCategory.electronics:
        return 'Electronics';
    }
  }

  // Serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category.toString(),
      'productionCost': productionCost,
      'sellingPrice': sellingPrice,
      'stock': stock,
      'quality': quality,
      'demand': demand,
      'unitsSold': unitsSold,
      'lastRestocked': lastRestocked.millisecondsSinceEpoch,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      category: ProductCategory.values.firstWhere(
        (e) => e.toString() == map['category'],
        orElse: () => ProductCategory.software,
      ),
      productionCost: map['productionCost']?.toDouble() ?? 10.0,
      sellingPrice: map['sellingPrice']?.toDouble() ?? 20.0,
      stock: map['stock'] ?? 0,
      quality: map['quality']?.toDouble() ?? 1.0,
      demand: map['demand']?.toDouble() ?? 1.0,
      unitsSold: map['unitsSold'] ?? 0,
      lastRestocked: DateTime.fromMillisecondsSinceEpoch(map['lastRestocked']),
    );
  }

  Product copyWith({
    String? id,
    String? name,
    ProductCategory? category,
    double? productionCost,
    double? sellingPrice,
    int? stock,
    double? quality,
    double? demand,
    int? unitsSold,
    DateTime? lastRestocked,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      productionCost: productionCost ?? this.productionCost,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      stock: stock ?? this.stock,
      quality: quality ?? this.quality,
      demand: demand ?? this.demand,
      unitsSold: unitsSold ?? this.unitsSold,
      lastRestocked: lastRestocked ?? this.lastRestocked,
    );
  }

  @override
  String toString() {
    return 'Product(name: $name, price: \$${sellingPrice.toStringAsFixed(2)}, stock: $stock, profit: \$${profitPerUnit.toStringAsFixed(2)})';
  }
}

// Product templates
class ProductTemplates {
  static List<Product> getTechnologyProducts() {
    return [
      Product(
        id: 'tech_1',
        name: 'Mobile App',
        category: ProductCategory.software,
        productionCost: 5000,
        sellingPrice: 15000,
      ),
      Product(
        id: 'tech_2',
        name: 'Web Platform',
        category: ProductCategory.software,
        productionCost: 8000,
        sellingPrice: 20000,
      ),
      Product(
        id: 'tech_3',
        name: 'Smart Device',
        category: ProductCategory.hardware,
        productionCost: 200,
        sellingPrice: 500,
      ),
    ];
  }

  static List<Product> getRetailProducts() {
    return [
      Product(
        id: 'retail_1',
        name: 'T-Shirt',
        category: ProductCategory.clothing,
        productionCost: 5,
        sellingPrice: 25,
      ),
      Product(
        id: 'retail_2',
        name: 'Jeans',
        category: ProductCategory.clothing,
        productionCost: 15,
        sellingPrice: 60,
      ),
      Product(
        id: 'retail_3',
        name: 'Sneakers',
        category: ProductCategory.clothing,
        productionCost: 30,
        sellingPrice: 120,
      ),
    ];
  }

  static List<Product> getManufacturingProducts() {
    return [
      Product(
        id: 'mfg_1',
        name: 'Widget A',
        category: ProductCategory.hardware,
        productionCost: 10,
        sellingPrice: 25,
      ),
      Product(
        id: 'mfg_2',
        name: 'Component B',
        category: ProductCategory.hardware,
        productionCost: 50,
        sellingPrice: 120,
      ),
    ];
  }

  static List<Product> getFoodProducts() {
    return [
      Product(
        id: 'food_1',
        name: 'Sandwich',
        category: ProductCategory.food,
        productionCost: 3,
        sellingPrice: 8,
      ),
      Product(
        id: 'food_2',
        name: 'Coffee',
        category: ProductCategory.food,
        productionCost: 1,
        sellingPrice: 4,
      ),
    ];
  }
}
