import 'dart:async';
import 'dart:math';
import '../models/game_state.dart';
import '../models/player.dart';
import '../models/bullet.dart';
import '../models/game_object.dart';
import 'collision_detector.dart';
import 'level_manager.dart';

class GameEngine {
  GameState gameState;
  Timer? gameTimer;
  double screenWidth;
  double screenHeight;
  Function(int)? onScoreChanged;
  Function(int)? onLivesChanged;
  Function(int)? onLevelChanged;
  Function()? onGameOver;
  Function()? onLevelComplete;
  Function(String)? onSoundEffect;

  static const double targetFPS = 60.0;
  static const double deltaTime = 1.0 / targetFPS;

  GameEngine({
    required this.screenWidth,
    required this.screenHeight,
    this.onScoreChanged,
    this.onLivesChanged,
    this.onLevelChanged,
    this.onGameOver,
    this.onLevelComplete,
    this.onSoundEffect,
  }) : gameState = GameState(
          player: Player(
            x: screenWidth / 2 - 20,
            y: screenHeight - 60,
          ),
        );

  void startGame() {
    gameState.reset();
    _initializeLevel();
    _startGameLoop();
  }

  void pauseGame() {
    gameState.pause();
    gameTimer?.cancel();
  }

  void resumeGame() {
    gameState.resume();
    _startGameLoop();
  }

  void stopGame() {
    gameTimer?.cancel();
    gameState.gameOver();
  }

  void _startGameLoop() {
    gameTimer?.cancel();
    gameTimer = Timer.periodic(
      Duration(milliseconds: (1000 / targetFPS).round()),
      (timer) => _update(),
    );
  }

  void _update() {
    if (gameState.status != GameStatus.playing) return;

    gameState.gameTime += deltaTime;

    // Update player
    gameState.player.update(deltaTime);

    // Update aliens
    _updateAliens();

    // Update bullets
    _updateBullets();

    // Handle alien shooting
    _handleAlienShooting();

    // Check collisions
    _handleCollisions();

    // Check game over conditions
    _checkGameOverConditions();

    // Check level complete
    _checkLevelComplete();
  }

  void _updateAliens() {
    gameState.alienMoveTimer += deltaTime;

    if (gameState.alienMoveTimer >= gameState.alienMoveInterval) {
      gameState.alienMoveTimer = 0.0;

      // Check if aliens need to move down
      bool shouldMoveDown = false;
      if (gameState.isMovingRight) {
        double rightmostX = gameState.aliens.isEmpty ? 0 :
          gameState.aliens.map((a) => a.x + a.width).reduce(max);
        if (rightmostX >= screenWidth - 10) {
          shouldMoveDown = true;
          gameState.isMovingRight = false;
        }
      } else {
        double leftmostX = gameState.aliens.isEmpty ? screenWidth :
          gameState.aliens.map((a) => a.x).reduce(min);
        if (leftmostX <= 10) {
          shouldMoveDown = true;
          gameState.isMovingRight = true;
        }
      }

      if (shouldMoveDown) {
        // Move all aliens down
        for (var alien in gameState.aliens) {
          alien.moveDown(gameState.alienDropDistance);
        }
      } else {
        // Move aliens horizontally
        double direction = gameState.isMovingRight ? 1.0 : -1.0;
        for (var alien in gameState.aliens) {
          alien.moveHorizontal(gameState.alienMoveInterval, direction);
        }
      }
    }

    // Update individual aliens
    for (var alien in gameState.aliens) {
      alien.update(deltaTime);
    }
  }

  void _updateBullets() {
    // Update player bullets
    for (int i = gameState.playerBullets.length - 1; i >= 0; i--) {
      final bullet = gameState.playerBullets[i];
      bullet.update(deltaTime);

      if (!bullet.isOnScreen(GameSize(screenWidth, screenHeight))) {
        gameState.playerBullets.removeAt(i);
      }
    }

    // Update alien bullets
    for (int i = gameState.alienBullets.length - 1; i >= 0; i--) {
      final bullet = gameState.alienBullets[i];
      bullet.update(deltaTime);

      if (!bullet.isOnScreen(GameSize(screenWidth, screenHeight))) {
        gameState.alienBullets.removeAt(i);
      }
    }
  }

  void _handleAlienShooting() {
    if (gameState.aliens.isEmpty) return;

    final currentTime = gameState.gameTime;
    final random = Random();

    // Randomly select an alien to potentially shoot
    if (random.nextDouble() < 0.02) { // 2% chance per frame
      final shootingAlien = gameState.aliens[random.nextInt(gameState.aliens.length)];

      if (shootingAlien.shouldShoot(currentTime)) {
        shootingAlien.shoot();

        final bullet = Bullet(
          x: shootingAlien.x + shootingAlien.width / 2,
          y: shootingAlien.y + shootingAlien.height,
          type: BulletType.alien,
        );

        gameState.alienBullets.add(bullet);
        onSoundEffect?.call('alien_shoot');
      }
    }
  }

  void _handleCollisions() {
    final collisions = CollisionDetector.checkCollisions(
      gameState.player,
      gameState.aliens,
      gameState.playerBullets,
      gameState.alienBullets,
    );

    for (final collision in collisions) {
      switch (collision.type) {
        case CollisionType.playerBulletAlien:
          _handlePlayerBulletAlienCollision(collision);
          break;
        case CollisionType.alienBulletPlayer:
          _handleAlienBulletPlayerCollision(collision);
          break;
        case CollisionType.alienPlayer:
          _handleAlienPlayerCollision(collision);
          break;
        case CollisionType.bulletBullet:
          _handleBulletBulletCollision(collision);
          break;
      }
    }
  }

  void _handlePlayerBulletAlienCollision(CollisionResult collision) {
    if (collision.alien != null && collision.bulletIndex != null && collision.alienIndex != null) {
      // Add score
      gameState.addScore(collision.alien!.points);
      onScoreChanged?.call(gameState.score);

      // Remove bullet and alien
      gameState.playerBullets.removeAt(collision.bulletIndex!);
      gameState.aliens.removeAt(collision.alienIndex!);

      onSoundEffect?.call('alien_destroyed');
    }
  }

  void _handleAlienBulletPlayerCollision(CollisionResult collision) {
    if (collision.bulletIndex != null) {
      // Remove bullet
      gameState.alienBullets.removeAt(collision.bulletIndex!);

      // Damage player
      gameState.player.takeDamage();
      onLivesChanged?.call(gameState.player.lives);
      onSoundEffect?.call('player_hit');
    }
  }

  void _handleAlienPlayerCollision(CollisionResult collision) {
    // Game over - alien reached player
    gameState.player.lives = 0;
    onLivesChanged?.call(gameState.player.lives);
    onSoundEffect?.call('game_over');
  }

  void _handleBulletBulletCollision(CollisionResult collision) {
    if (collision.bulletIndex != null && collision.otherBulletIndex != null) {
      // Remove both bullets
      gameState.playerBullets.removeAt(collision.bulletIndex!);
      gameState.alienBullets.removeAt(collision.otherBulletIndex!);
      onSoundEffect?.call('bullet_collision');
    }
  }

  void _checkGameOverConditions() {
    if (gameState.isGameOver()) {
      stopGame();
      onGameOver?.call();
    }
  }

  void _checkLevelComplete() {
    if (gameState.isLevelComplete()) {
      gameState.status = GameStatus.levelComplete;
      gameTimer?.cancel();
      onLevelComplete?.call();
    }
  }

  void nextLevel() {
    gameState.nextLevel();
    onLevelChanged?.call(gameState.level);
    _initializeLevel();
    _startGameLoop();
  }

  void _initializeLevel() {
    gameState.aliens = LevelManager.generateAliensForLevel(
      gameState.level,
      screenWidth,
      screenHeight,
    );
  }

  // Player controls
  void movePlayerLeft() {
    if (gameState.status == GameStatus.playing) {
      gameState.player.moveLeft(deltaTime, screenWidth);
    }
  }

  void movePlayerRight() {
    if (gameState.status == GameStatus.playing) {
      gameState.player.moveRight(deltaTime, screenWidth);
    }
  }

  void playerShoot() {
    if (gameState.status == GameStatus.playing && gameState.player.shoot()) {
      final bullet = Bullet(
        x: gameState.player.x + gameState.player.width / 2,
        y: gameState.player.y,
        type: BulletType.player,
      );

      gameState.playerBullets.add(bullet);
      onSoundEffect?.call('player_shoot');
    }
  }

  void dispose() {
    gameTimer?.cancel();
  }
}


