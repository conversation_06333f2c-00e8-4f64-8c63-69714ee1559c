import '../models/game_object.dart';
import '../models/player.dart';
import '../models/alien.dart';
import '../models/bullet.dart';

class CollisionDetector {
  static List<CollisionResult> checkCollisions(
    Player player,
    List<Alien> aliens,
    List<Bullet> playerBullets,
    List<Bullet> alienBullets,
  ) {
    List<CollisionResult> results = [];

    // Check player bullets vs aliens
    for (int i = playerBullets.length - 1; i >= 0; i--) {
      final bullet = playerBullets[i];
      for (int j = aliens.length - 1; j >= 0; j--) {
        final alien = aliens[j];
        if (bullet.collidesWith(alien)) {
          results.add(CollisionResult(
            type: CollisionType.playerBulletAlien,
            bullet: bullet,
            alien: alien,
            bulletIndex: i,
            alienIndex: j,
          ));
          break; // Bullet can only hit one alien
        }
      }
    }

    // Check alien bullets vs player
    for (int i = alienBullets.length - 1; i >= 0; i--) {
      final bullet = alienBullets[i];
      if (bullet.collidesWith(player)) {
        results.add(CollisionResult(
          type: CollisionType.alienBulletPlayer,
          bullet: bullet,
          player: player,
          bulletIndex: i,
        ));
      }
    }

    // Check aliens vs player (direct collision)
    for (int i = aliens.length - 1; i >= 0; i--) {
      final alien = aliens[i];
      if (alien.collidesWith(player)) {
        results.add(CollisionResult(
          type: CollisionType.alienPlayer,
          alien: alien,
          player: player,
          alienIndex: i,
        ));
      }
    }

    // Check bullet vs bullet collisions (optional feature)
    for (int i = playerBullets.length - 1; i >= 0; i--) {
      final playerBullet = playerBullets[i];
      for (int j = alienBullets.length - 1; j >= 0; j--) {
        final alienBullet = alienBullets[j];
        if (playerBullet.collidesWith(alienBullet)) {
          results.add(CollisionResult(
            type: CollisionType.bulletBullet,
            bullet: playerBullet,
            otherBullet: alienBullet,
            bulletIndex: i,
            otherBulletIndex: j,
          ));
        }
      }
    }

    return results;
  }

  static bool isPointInRect(double px, double py, double rx, double ry, double rw, double rh) {
    return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
  }

  static bool areRectsOverlapping(
    double r1x, double r1y, double r1w, double r1h,
    double r2x, double r2y, double r2w, double r2h,
  ) {
    return r1x < r2x + r2w &&
           r1x + r1w > r2x &&
           r1y < r2y + r2h &&
           r1y + r1h > r2y;
  }
}

enum CollisionType {
  playerBulletAlien,
  alienBulletPlayer,
  alienPlayer,
  bulletBullet,
}

class CollisionResult {
  final CollisionType type;
  final Bullet? bullet;
  final Bullet? otherBullet;
  final Alien? alien;
  final Player? player;
  final int? bulletIndex;
  final int? otherBulletIndex;
  final int? alienIndex;

  CollisionResult({
    required this.type,
    this.bullet,
    this.otherBullet,
    this.alien,
    this.player,
    this.bulletIndex,
    this.otherBulletIndex,
    this.alienIndex,
  });
}
