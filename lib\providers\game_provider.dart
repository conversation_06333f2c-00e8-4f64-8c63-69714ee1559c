import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/character.dart';
import '../models/character_stats.dart';
import '../models/inventory.dart';
import '../models/item.dart';
import '../models/enemy.dart';
import '../database/database_helper.dart';
import '../game/battle_system.dart';

class GameProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Character? _currentCharacter;
  Inventory _inventory = Inventory();
  BattleState? _currentBattle;
  List<Character> _savedCharacters = [];
  bool _isLoading = false;

  // Getters
  Character? get currentCharacter => _currentCharacter;
  Inventory get inventory => _inventory;
  BattleState? get currentBattle => _currentBattle;
  List<Character> get savedCharacters => _savedCharacters;
  bool get isLoading => _isLoading;
  bool get isInBattle => _currentBattle != null;

  // Character management
  Future<void> createNewCharacter(String name) async {
    _isLoading = true;
    notifyListeners();

    try {
      final characterId = _uuid.v4();
      final character = Character(
        id: characterId,
        name: name,
        baseStats: CharacterStats(),
      );

      // Create starting inventory with basic items
      final startingInventory = Inventory()
          .addItem(ItemDatabase.getItemById('health_potion')!, quantity: 3)
          .addItem(ItemDatabase.getItemById('rusty_sword')!)
          .addItem(ItemDatabase.getItemById('leather_armor')!);

      await _databaseHelper.saveCharacter(character);
      await _databaseHelper.saveInventory(characterId, startingInventory);

      _currentCharacter = character;
      _inventory = startingInventory;
      
      await loadSavedCharacters();
    } catch (e) {
      debugPrint('Error creating character: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadCharacter(String characterId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final character = await _databaseHelper.loadCharacter(characterId);
      final inventory = await _databaseHelper.loadInventory(characterId);

      if (character != null) {
        _currentCharacter = character;
        _inventory = inventory;
      }
    } catch (e) {
      debugPrint('Error loading character: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> saveCurrentCharacter() async {
    if (_currentCharacter == null) return;

    try {
      await _databaseHelper.updateCharacter(_currentCharacter!);
      await _databaseHelper.saveInventory(_currentCharacter!.id, _inventory);
    } catch (e) {
      debugPrint('Error saving character: $e');
    }
  }

  Future<void> loadSavedCharacters() async {
    try {
      _savedCharacters = await _databaseHelper.getAllCharacters();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading saved characters: $e');
    }
  }

  Future<void> deleteCharacter(String characterId) async {
    try {
      await _databaseHelper.deleteCharacter(characterId);
      await loadSavedCharacters();
      
      // If deleted character was current, clear it
      if (_currentCharacter?.id == characterId) {
        _currentCharacter = null;
        _inventory = Inventory();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting character: $e');
    }
  }

  // Inventory management
  void addItemToInventory(Item item, {int quantity = 1}) {
    _inventory = _inventory.addItem(item, quantity: quantity);
    notifyListeners();
    saveCurrentCharacter();
  }

  void removeItemFromInventory(String itemId, {int quantity = 1}) {
    _inventory = _inventory.removeItem(itemId, quantity: quantity);
    notifyListeners();
    saveCurrentCharacter();
  }

  void equipItem(Item item) {
    if (_currentCharacter == null) return;

    // Remove item from inventory
    _inventory = _inventory.removeItem(item.id);
    
    // If there's already an item equipped in this slot, add it back to inventory
    final currentEquipped = _getCurrentEquippedItem(item.type);
    if (currentEquipped != null) {
      _inventory = _inventory.addItem(currentEquipped);
    }

    // Equip the new item
    _currentCharacter = _currentCharacter!.equipItem(item);
    
    notifyListeners();
    saveCurrentCharacter();
  }

  void unequipItem(ItemType type) {
    if (_currentCharacter == null) return;

    final currentEquipped = _getCurrentEquippedItem(type);
    if (currentEquipped != null) {
      // Add item back to inventory
      _inventory = _inventory.addItem(currentEquipped);
      
      // Unequip the item
      _currentCharacter = _currentCharacter!.unequipItem(type);
      
      notifyListeners();
      saveCurrentCharacter();
    }
  }

  Item? _getCurrentEquippedItem(ItemType type) {
    if (_currentCharacter == null) return null;
    
    switch (type) {
      case ItemType.weapon:
        return _currentCharacter!.equipment.weapon;
      case ItemType.armor:
        return _currentCharacter!.equipment.armor;
      case ItemType.accessory:
        return _currentCharacter!.equipment.accessory;
      case ItemType.consumable:
        return null;
    }
  }

  void useConsumableItem(Item item) {
    if (_currentCharacter == null || !item.consumable) return;

    // Remove item from inventory
    _inventory = _inventory.removeItem(item.id);

    // Apply item effect
    switch (item.effect) {
      case 'heal':
        final healAmount = item.effectValue ?? 0;
        _currentCharacter = _currentCharacter!.heal(healAmount);
        break;
      case 'attack_buff':
        // Temporary buffs are handled in battle
        break;
    }

    notifyListeners();
    saveCurrentCharacter();
  }

  // Battle system
  void startBattle(Enemy enemy) {
    if (_currentCharacter == null) return;

    _currentBattle = BattleSystem.initializeBattle(_currentCharacter!, enemy);
    _currentBattle = BattleSystem.startTurn(_currentBattle!);
    notifyListeners();
  }

  void playerAttack() {
    if (_currentBattle == null) return;

    _currentBattle = BattleSystem.playerAttack(_currentBattle!);
    
    // Update character state
    _currentCharacter = _currentBattle!.player;
    
    // Check if battle continues
    if (_currentBattle!.phase == BattlePhase.enemyTurn) {
      // Delay enemy turn for better UX
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (_currentBattle != null) {
          _currentBattle = BattleSystem.enemyTurn(_currentBattle!);
          _currentCharacter = _currentBattle!.player;
          notifyListeners();
        }
      });
    }
    
    notifyListeners();
  }

  void playerDefend() {
    if (_currentBattle == null) return;

    _currentBattle = BattleSystem.playerDefend(_currentBattle!);
    
    // Enemy turn
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (_currentBattle != null) {
        _currentBattle = BattleSystem.enemyTurn(_currentBattle!);
        _currentCharacter = _currentBattle!.player;
        notifyListeners();
      }
    });
    
    notifyListeners();
  }

  void playerUseItem(Item item) {
    if (_currentBattle == null || !item.consumable) return;

    // Remove item from inventory
    _inventory = _inventory.removeItem(item.id);
    
    _currentBattle = BattleSystem.playerUseItem(_currentBattle!, item);
    _currentCharacter = _currentBattle!.player;
    
    // Enemy turn
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (_currentBattle != null) {
        _currentBattle = BattleSystem.enemyTurn(_currentBattle!);
        _currentCharacter = _currentBattle!.player;
        notifyListeners();
      }
    });
    
    notifyListeners();
  }

  void playerFlee() {
    if (_currentBattle == null) return;

    _currentBattle = BattleSystem.playerFlee(_currentBattle!);
    notifyListeners();
  }

  void endBattle() {
    if (_currentBattle == null) return;

    // Apply rewards if victory
    if (_currentBattle!.phase == BattlePhase.victory) {
      final rewards = BattleSystem.calculateRewards(_currentBattle!.enemy);
      _currentCharacter = BattleSystem.applyVictoryRewards(_currentCharacter!, rewards);
      
      // Add items to inventory
      for (final item in rewards.items) {
        _inventory = _inventory.addItem(item);
      }
    }

    _currentBattle = null;
    notifyListeners();
    saveCurrentCharacter();
  }

  // Utility methods
  void startRandomBattle() {
    if (_currentCharacter == null) return;
    
    final enemy = EnemyDatabase.getRandomEnemy(
      maxLevel: _currentCharacter!.baseStats.level + 2,
    );
    startBattle(enemy);
  }

  void restoreCharacterHealth() {
    if (_currentCharacter == null) return;
    
    _currentCharacter = _currentCharacter!.copyWith(
      baseStats: _currentCharacter!.baseStats.copyWith(
        currentHp: _currentCharacter!.totalStats.maxHp,
      ),
    );
    
    notifyListeners();
    saveCurrentCharacter();
  }

  @override
  void dispose() {
    _databaseHelper.close();
    super.dispose();
  }
}
