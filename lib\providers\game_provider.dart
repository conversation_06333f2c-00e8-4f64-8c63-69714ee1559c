import 'package:flutter/foundation.dart';
import '../game/game_engine.dart';
import '../models/game_state.dart';
import '../database/database_helper.dart';
import '../models/high_score.dart';

class GameProvider extends ChangeNotifier {
  GameEngine? _gameEngine;
  GameState? _gameState;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  int _score = 0;
  int _lives = 3;
  int _level = 1;
  bool _isGameActive = false;
  bool _isPaused = false;
  String? _lastSoundEffect;

  // Getters
  GameEngine? get gameEngine => _gameEngine;
  GameState? get gameState => _gameState;
  int get score => _score;
  int get lives => _lives;
  int get level => _level;
  bool get isGameActive => _isGameActive;
  bool get isPaused => _isPaused;
  String? get lastSoundEffect => _lastSoundEffect;

  void initializeGame(double screenWidth, double screenHeight) {
    _gameEngine = GameEngine(
      screenWidth: screenWidth,
      screenHeight: screenHeight,
      onScoreChanged: _onScoreChanged,
      onLivesChanged: _onLivesChanged,
      onLevelChanged: _onLevelChanged,
      onGameOver: _onGameOver,
      onLevelComplete: _onLevelComplete,
      onSoundEffect: _onSoundEffect,
    );
    
    _gameState = _gameEngine!.gameState;
    notifyListeners();
  }

  void startNewGame() {
    if (_gameEngine != null) {
      _gameEngine!.startGame();
      _isGameActive = true;
      _isPaused = false;
      _score = 0;
      _lives = 3;
      _level = 1;
      notifyListeners();
    }
  }

  void pauseGame() {
    if (_gameEngine != null && _isGameActive) {
      _gameEngine!.pauseGame();
      _isPaused = true;
      notifyListeners();
    }
  }

  void resumeGame() {
    if (_gameEngine != null && _isPaused) {
      _gameEngine!.resumeGame();
      _isPaused = false;
      notifyListeners();
    }
  }

  void stopGame() {
    if (_gameEngine != null) {
      _gameEngine!.stopGame();
      _isGameActive = false;
      _isPaused = false;
      notifyListeners();
    }
  }

  void nextLevel() {
    if (_gameEngine != null) {
      _gameEngine!.nextLevel();
      notifyListeners();
    }
  }

  // Player controls
  void movePlayerLeft() {
    _gameEngine?.movePlayerLeft();
  }

  void movePlayerRight() {
    _gameEngine?.movePlayerRight();
  }

  void playerShoot() {
    _gameEngine?.playerShoot();
  }

  // Callbacks from game engine
  void _onScoreChanged(int newScore) {
    _score = newScore;
    notifyListeners();
  }

  void _onLivesChanged(int newLives) {
    _lives = newLives;
    notifyListeners();
  }

  void _onLevelChanged(int newLevel) {
    _level = newLevel;
    notifyListeners();
  }

  void _onGameOver() {
    _isGameActive = false;
    _isPaused = false;
    notifyListeners();
  }

  void _onLevelComplete() {
    notifyListeners();
  }

  void _onSoundEffect(String soundName) {
    _lastSoundEffect = soundName;
    notifyListeners();
  }

  // High score management
  Future<bool> isHighScore() async {
    return await _databaseHelper.isHighScore(_score);
  }

  Future<void> saveHighScore(String playerName) async {
    final highScore = HighScore(
      playerName: playerName,
      score: _score,
      date: DateTime.now(),
    );
    await _databaseHelper.insertHighScore(highScore);
  }

  Future<List<HighScore>> getHighScores() async {
    return await _databaseHelper.getHighScores();
  }

  @override
  void dispose() {
    _gameEngine?.dispose();
    super.dispose();
  }
}
