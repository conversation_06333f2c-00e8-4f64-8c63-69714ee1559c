import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../game/battle_system.dart';
import '../models/item.dart';

class BattleScreen extends StatefulWidget {
  const BattleScreen({super.key});

  @override
  State<BattleScreen> createState() => _BattleScreenState();
}

class _BattleScreenState extends State<BattleScreen>
    with TickerProviderStateMixin {
  late AnimationController _shakeController;
  late AnimationController _fadeController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2D1B69),
              Color(0xFF11052C),
              Color(0xFF000000),
            ],
          ),
        ),
        child: Consumer<GameProvider>(
          builder: (context, gameProvider, child) {
            final battle = gameProvider.currentBattle;

            if (battle == null) {
              return const Center(child: CircularProgressIndicator());
            }

            return FadeTransition(
              opacity: _fadeAnimation,
              child: SafeArea(
                child: Column(
                  children: [
                    _buildBattleHeader(battle),
                    Expanded(
                      child: _buildBattleField(battle),
                    ),
                    _buildBattleLog(battle),
                    _buildActionButtons(battle, gameProvider),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBattleHeader(BattleState battle) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Turn ${battle.turnCount}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getPhaseColor(battle.phase).withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: _getPhaseColor(battle.phase)),
            ),
            child: Text(
              _getPhaseText(battle.phase),
              style: TextStyle(
                color: _getPhaseColor(battle.phase),
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBattleField(BattleState battle) {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Player Side
                Expanded(
                  child: _buildCharacterDisplay(
                    battle.player.name,
                    battle.player.baseStats.currentHp,
                    battle.player.totalStats.maxHp,
                    Icons.person,
                    Colors.blue,
                    isPlayer: true,
                    isDefending: battle.playerDefending,
                  ),
                ),

                // VS Indicator
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.red, width: 3),
                    color: Colors.red.withOpacity(0.1),
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: Colors.red,
                    size: 30,
                  ),
                ),

                // Enemy Side
                Expanded(
                  child: _buildCharacterDisplay(
                    battle.enemy.name,
                    battle.enemy.stats.currentHp,
                    battle.enemy.stats.maxHp,
                    Icons.bug_report,
                    Colors.red,
                    isPlayer: false,
                    isDefending: battle.enemyDefending,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCharacterDisplay(
    String name,
    int currentHp,
    int maxHp,
    IconData icon,
    Color color, {
    required bool isPlayer,
    required bool isDefending,
  }) {
    final hpPercentage = currentHp / maxHp;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Character Avatar
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isDefending ? Colors.yellow : color,
              width: isDefending ? 4 : 3,
            ),
            gradient: LinearGradient(
              colors: [
                color.withOpacity(0.3),
                color.withOpacity(0.1),
              ],
            ),
            boxShadow: isDefending
                ? [
                    BoxShadow(
                      color: Colors.yellow.withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ]
                : null,
          ),
          child: Icon(
            icon,
            size: 40,
            color: color,
          ),
        ),

        const SizedBox(height: 12),

        // Character Name
        Text(
          name,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        // Health Bar
        Container(
          width: 120,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'HP',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$currentHp/$maxHp',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: hpPercentage,
                backgroundColor: Colors.red.withOpacity(0.3),
                valueColor: AlwaysStoppedAnimation<Color>(
                  hpPercentage > 0.6 ? Colors.green :
                  hpPercentage > 0.3 ? Colors.orange : Colors.red,
                ),
                minHeight: 8,
              ),
            ],
          ),
        ),

        if (isDefending) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.yellow.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.yellow),
            ),
            child: const Text(
              'DEFENDING',
              style: TextStyle(
                color: Colors.yellow,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBattleLog(BattleState battle) {
    return Container(
      height: 120,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Battle Log',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              reverse: true,
              itemCount: battle.battleLog.length,
              itemBuilder: (context, index) {
                final logIndex = battle.battleLog.length - 1 - index;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Text(
                    battle.battleLog[logIndex],
                    style: TextStyle(
                      color: index == 0 ? Colors.white : Colors.white70,
                      fontSize: 12,
                      fontWeight: index == 0 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BattleState battle, GameProvider gameProvider) {
    if (battle.phase == BattlePhase.victory) {
      return _buildVictoryButtons(battle, gameProvider);
    }

    if (battle.phase == BattlePhase.defeat) {
      return _buildDefeatButtons(gameProvider);
    }

    if (battle.phase == BattlePhase.fled) {
      return _buildFleeButtons(gameProvider);
    }

    if (battle.phase != BattlePhase.playerTurn) {
      return Container(
        height: 100,
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Enemy is thinking...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Attack',
                  Icons.local_fire_department,
                  Colors.red,
                  () {
                    _triggerShake();
                    gameProvider.playerAttack();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'Defend',
                  Icons.shield,
                  Colors.blue,
                  () => gameProvider.playerDefend(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Items',
                  Icons.backpack,
                  Colors.green,
                  () => _showItemsDialog(gameProvider),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'Flee',
                  Icons.directions_run,
                  Colors.orange,
                  () => gameProvider.playerFlee(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.withOpacity(0.2),
          foregroundColor: color,
          side: BorderSide(color: color, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVictoryButtons(BattleState battle, GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.emoji_events, color: Colors.green, size: 24),
                SizedBox(width: 8),
                Text(
                  'VICTORY!',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () => gameProvider.endBattle(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'Continue Adventure',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefeatButtons(GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.close, color: Colors.red, size: 24),
                SizedBox(width: 8),
                Text(
                  'DEFEAT!',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () {
                gameProvider.endBattle();
                gameProvider.restoreCharacterHealth(); // Mercy heal
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'Return to Town',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFleeButtons(GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.directions_run, color: Colors.orange, size: 24),
                SizedBox(width: 8),
                Text(
                  'ESCAPED!',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () => gameProvider.endBattle(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'Continue Adventure',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showItemsDialog(GameProvider gameProvider) {
    final consumables = gameProvider.inventory.consumables;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Use Item'),
        content: SizedBox(
          width: double.maxFinite,
          height: 200,
          child: consumables.isEmpty
              ? const Center(
                  child: Text(
                    'No usable items',
                    style: TextStyle(color: Colors.white70),
                  ),
                )
              : ListView.builder(
                  itemCount: consumables.length,
                  itemBuilder: (context, index) {
                    final slot = consumables[index];
                    return ListTile(
                      leading: Icon(
                        Icons.local_pharmacy,
                        color: Colors.green,
                      ),
                      title: Text(
                        slot.item.name,
                        style: const TextStyle(color: Colors.white),
                      ),
                      subtitle: Text(
                        'Quantity: ${slot.quantity}',
                        style: const TextStyle(color: Colors.white70),
                      ),
                      onTap: () {
                        gameProvider.playerUseItem(slot.item);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Color _getPhaseColor(BattlePhase phase) {
    switch (phase) {
      case BattlePhase.start:
        return Colors.blue;
      case BattlePhase.playerTurn:
        return Colors.green;
      case BattlePhase.enemyTurn:
        return Colors.red;
      case BattlePhase.victory:
        return Colors.green;
      case BattlePhase.defeat:
        return Colors.red;
      case BattlePhase.fled:
        return Colors.orange;
    }
  }

  String _getPhaseText(BattlePhase phase) {
    switch (phase) {
      case BattlePhase.start:
        return 'BATTLE START';
      case BattlePhase.playerTurn:
        return 'YOUR TURN';
      case BattlePhase.enemyTurn:
        return 'ENEMY TURN';
      case BattlePhase.victory:
        return 'VICTORY';
      case BattlePhase.defeat:
        return 'DEFEAT';
      case BattlePhase.fled:
        return 'ESCAPED';
    }
  }

  void _triggerShake() {
    _shakeController.forward().then((_) {
      _shakeController.reverse();
    });
  }
}
