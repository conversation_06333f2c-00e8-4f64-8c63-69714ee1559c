import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/business_provider.dart';
import 'business_creation_screen.dart';
import 'business_selection_screen.dart';
import 'dashboard_screen.dart';

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();

    // Load saved businesses
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BusinessProvider>().loadSavedBusinesses();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0D1421),
              Color(0xFF1E2A3A),
              Color(0xFF2C3E50),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                               MediaQuery.of(context).padding.top -
                               MediaQuery.of(context).padding.bottom - 40,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 20),
                      _buildHeader(),
                      const SizedBox(height: 30),
                      _buildMenuButtons(),
                      const SizedBox(height: 30),
                      _buildFooter(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xFF2196F3), Color(0xFF21CBF3)],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 3,
              ),
            ],
          ),
          child: const Icon(
            Icons.business,
            size: 50,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'Business Tycoon',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 1.2,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          'Build Your Corporate Empire',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.7),
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuButtons() {
    return Consumer<BusinessProvider>(
      builder: (context, businessProvider, child) {
        return Column(
          children: [
            _buildMenuButton(
              icon: Icons.add_business,
              title: 'Start New Business',
              subtitle: 'Begin your entrepreneurial journey',
              onTap: () => _navigateToBusinessCreation(),
              color: const Color(0xFF4CAF50),
            ),
            const SizedBox(height: 12),
            _buildMenuButton(
              icon: Icons.folder_open,
              title: 'Load Business',
              subtitle: businessProvider.savedBusinesses.isEmpty
                  ? 'No saved businesses found'
                  : '${businessProvider.savedBusinesses.length} saved business${businessProvider.savedBusinesses.length == 1 ? '' : 'es'}',
              onTap: businessProvider.savedBusinesses.isEmpty
                  ? null
                  : () => _navigateToBusinessSelection(),
              color: const Color(0xFF2196F3),
            ),
            const SizedBox(height: 12),
            _buildMenuButton(
              icon: Icons.dashboard,
              title: 'Continue',
              subtitle: businessProvider.currentBusiness != null
                  ? 'Resume ${businessProvider.currentBusiness!.name}'
                  : 'No active business',
              onTap: businessProvider.currentBusiness != null
                  ? () => _navigateToDashboard()
                  : null,
              color: const Color(0xFFFF9800),
            ),
            const SizedBox(height: 20),
            _buildMenuButton(
              icon: Icons.info_outline,
              title: 'How to Play',
              subtitle: 'Learn the basics of business management',
              onTap: () => _showHowToPlay(),
              color: const Color(0xFF9C27B0),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMenuButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    required Color color,
  }) {
    final isEnabled = onTap != null;

    return Container(
      width: double.infinity,
      height: 70,
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: isEnabled
                    ? [color.withOpacity(0.8), color.withOpacity(0.6)]
                    : [Colors.grey.withOpacity(0.3), Colors.grey.withOpacity(0.2)],
              ),
              border: Border.all(
                color: isEnabled ? color.withOpacity(0.5) : Colors.grey.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isEnabled ? Colors.white.withOpacity(0.2) : Colors.grey.withOpacity(0.2),
                    ),
                    child: Icon(
                      icon,
                      color: isEnabled ? Colors.white : Colors.grey,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isEnabled ? Colors.white : Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            color: isEnabled ? Colors.white.withOpacity(0.8) : Colors.grey.withOpacity(0.8),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: isEnabled ? Colors.white.withOpacity(0.7) : Colors.grey.withOpacity(0.5),
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        Text(
          'Version 1.0.0',
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Made with Flutter & Sqflite',
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        ),
      ],
    );
  }

  void _navigateToBusinessCreation() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BusinessCreationScreen()),
    );
  }

  void _navigateToBusinessSelection() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BusinessSelectionScreen()),
    );
  }

  void _navigateToDashboard() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const DashboardScreen()),
    );
  }

  void _showHowToPlay() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('How to Play'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🏢 Start Your Business', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Choose an industry and create your company.'),
              SizedBox(height: 12),
              Text('👥 Hire Employees', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Recruit sales, engineers, and managers to grow your business.'),
              SizedBox(height: 12),
              Text('📦 Manage Products', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Develop, price, and stock products to sell.'),
              SizedBox(height: 12),
              Text('💰 Watch Your Finances', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Balance revenue and expenses to stay profitable.'),
              SizedBox(height: 12),
              Text('📈 Expand Your Empire', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Reach \$1B valuation to become a Business Tycoon!'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }
}
