import 'package:flutter/material.dart';
import 'game_screen.dart';
import 'high_scores_screen.dart';
import 'settings_screen.dart';

class MainMenuScreen extends StatelessWidget {
  const MainMenuScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF000011),
              Color(0xFF000033),
              Color(0xFF000055),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Game Title
              const Spacer(flex: 2),
              
              _buildTitle(),
              
              const Spacer(flex: 1),
              
              // Menu Buttons
              _buildMenuButtons(context),
              
              const Spacer(flex: 2),
              
              // Credits
              _buildCredits(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [Colors.cyan, Colors.blue, Colors.purple],
          ).createShader(bounds),
          child: const Text(
            'SPACE',
            style: TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 8,
            ),
          ),
        ),
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [Colors.red, Colors.orange, Colors.yellow],
          ).createShader(bounds),
          child: const Text(
            'INVADERS',
            style: TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 8,
            ),
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.cyan, width: 2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            'Classic Arcade Action',
            style: TextStyle(
              color: Colors.cyan,
              fontSize: 16,
              letterSpacing: 2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuButtons(BuildContext context) {
    return Column(
      children: [
        _buildMenuButton(
          'START GAME',
          Icons.play_arrow,
          Colors.green,
          () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const GameScreen()),
          ),
        ),
        const SizedBox(height: 20),
        _buildMenuButton(
          'HIGH SCORES',
          Icons.leaderboard,
          Colors.orange,
          () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const HighScoresScreen()),
          ),
        ),
        const SizedBox(height: 20),
        _buildMenuButton(
          'SETTINGS',
          Icons.settings,
          Colors.blue,
          () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SettingsScreen()),
          ),
        ),
        const SizedBox(height: 20),
        _buildMenuButton(
          'EXIT',
          Icons.exit_to_app,
          Colors.red,
          () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  Widget _buildMenuButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: 250,
      height: 60,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.withOpacity(0.2),
          foregroundColor: color,
          side: BorderSide(color: color, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          elevation: 8,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 12),
            Text(
              text,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCredits() {
    return const Column(
      children: [
        Text(
          'Built with Flutter & Sqflite',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
        SizedBox(height: 5),
        Text(
          'v1.0.0',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}
