import 'character_stats.dart';
import 'item.dart';

class Character {
  final String id;
  final String name;
  final CharacterStats baseStats;
  final Equipment equipment;
  final Map<String, int> temporaryBuffs; // For battle buffs

  Character({
    required this.id,
    required this.name,
    required this.baseStats,
    Equipment? equipment,
    Map<String, int>? temporaryBuffs,
  }) : equipment = equipment ?? Equipment(),
       temporaryBuffs = temporaryBuffs ?? {};

  // Calculate total stats including equipment and buffs
  CharacterStats get totalStats {
    final equipmentBonuses = equipment.getTotalBonuses();
    final buffedStats = baseStats.withEquipmentBonuses(
      attackBonus: equipmentBonuses['attack']! + (temporaryBuffs['attack'] ?? 0),
      defenseBonus: equipmentBonuses['defense']! + (temporaryBuffs['defense'] ?? 0),
      speedBonus: equipmentBonuses['speed']! + (temporaryBuffs['speed'] ?? 0),
      hpBonus: equipmentBonuses['hp']!,
    );
    
    return buffedStats;
  }

  Character copyWith({
    String? id,
    String? name,
    CharacterStats? baseStats,
    Equipment? equipment,
    Map<String, int>? temporaryBuffs,
  }) {
    return Character(
      id: id ?? this.id,
      name: name ?? this.name,
      baseStats: baseStats ?? this.baseStats,
      equipment: equipment ?? this.equipment,
      temporaryBuffs: temporaryBuffs ?? this.temporaryBuffs,
    );
  }

  // Equipment management
  Character equipItem(Item item) {
    final newEquipment = equipment.equipItem(item);
    return copyWith(equipment: newEquipment);
  }

  Character unequipItem(ItemType type) {
    final newEquipment = equipment.unequipItem(type);
    return copyWith(equipment: newEquipment);
  }

  // Battle methods
  Character takeDamage(int damage) {
    final actualDamage = (damage - totalStats.defense).clamp(1, damage);
    final newHp = (baseStats.currentHp - actualDamage).clamp(0, baseStats.maxHp);
    
    return copyWith(
      baseStats: baseStats.copyWith(currentHp: newHp),
    );
  }

  Character heal(int amount) {
    final newHp = (baseStats.currentHp + amount).clamp(0, totalStats.maxHp);
    return copyWith(
      baseStats: baseStats.copyWith(currentHp: newHp),
    );
  }

  Character addTemporaryBuff(String stat, int value) {
    final newBuffs = Map<String, int>.from(temporaryBuffs);
    newBuffs[stat] = (newBuffs[stat] ?? 0) + value;
    return copyWith(temporaryBuffs: newBuffs);
  }

  Character clearTemporaryBuffs() {
    return copyWith(temporaryBuffs: {});
  }

  Character gainExperience(int exp) {
    var newStats = baseStats.copyWith(
      experience: baseStats.experience + exp,
    );

    // Check for level up
    if (newStats.canLevelUp()) {
      newStats = newStats.levelUp();
    }

    return copyWith(baseStats: newStats);
  }

  Character addGold(int amount) {
    return copyWith(
      baseStats: baseStats.copyWith(gold: baseStats.gold + amount),
    );
  }

  bool get isAlive => baseStats.currentHp > 0;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'stats': baseStats.toMap(),
      'equipment': equipment.toMap(),
    };
  }

  factory Character.fromMap(Map<String, dynamic> map) {
    return Character(
      id: map['id'],
      name: map['name'],
      baseStats: CharacterStats.fromMap(map['stats']),
      equipment: Equipment.fromMap(map['equipment']),
    );
  }

  @override
  String toString() {
    return 'Character(name: $name, level: ${baseStats.level}, hp: ${baseStats.currentHp}/${totalStats.maxHp})';
  }
}

class Equipment {
  final Item? weapon;
  final Item? armor;
  final Item? accessory;

  Equipment({
    this.weapon,
    this.armor,
    this.accessory,
  });

  Equipment equipItem(Item item) {
    switch (item.type) {
      case ItemType.weapon:
        return Equipment(weapon: item, armor: armor, accessory: accessory);
      case ItemType.armor:
        return Equipment(weapon: weapon, armor: item, accessory: accessory);
      case ItemType.accessory:
        return Equipment(weapon: weapon, armor: armor, accessory: item);
      case ItemType.consumable:
        return this; // Can't equip consumables
    }
  }

  Equipment unequipItem(ItemType type) {
    switch (type) {
      case ItemType.weapon:
        return Equipment(weapon: null, armor: armor, accessory: accessory);
      case ItemType.armor:
        return Equipment(weapon: weapon, armor: null, accessory: accessory);
      case ItemType.accessory:
        return Equipment(weapon: weapon, armor: armor, accessory: null);
      case ItemType.consumable:
        return this;
    }
  }

  Map<String, int> getTotalBonuses() {
    final bonuses = {'attack': 0, 'defense': 0, 'speed': 0, 'hp': 0};
    
    if (weapon != null) {
      weapon!.stats.forEach((key, value) {
        bonuses[key] = (bonuses[key] ?? 0) + value;
      });
    }
    
    if (armor != null) {
      armor!.stats.forEach((key, value) {
        bonuses[key] = (bonuses[key] ?? 0) + value;
      });
    }
    
    if (accessory != null) {
      accessory!.stats.forEach((key, value) {
        bonuses[key] = (bonuses[key] ?? 0) + value;
      });
    }
    
    return bonuses;
  }

  List<Item> get equippedItems {
    final items = <Item>[];
    if (weapon != null) items.add(weapon!);
    if (armor != null) items.add(armor!);
    if (accessory != null) items.add(accessory!);
    return items;
  }

  Map<String, dynamic> toMap() {
    return {
      'weapon': weapon?.id,
      'armor': armor?.id,
      'accessory': accessory?.id,
    };
  }

  factory Equipment.fromMap(Map<String, dynamic> map) {
    return Equipment(
      weapon: map['weapon'] != null ? ItemDatabase.getItemById(map['weapon']) : null,
      armor: map['armor'] != null ? ItemDatabase.getItemById(map['armor']) : null,
      accessory: map['accessory'] != null ? ItemDatabase.getItemById(map['accessory']) : null,
    );
  }

  @override
  String toString() {
    return 'Equipment(weapon: ${weapon?.name}, armor: ${armor?.name}, accessory: ${accessory?.name})';
  }
}
