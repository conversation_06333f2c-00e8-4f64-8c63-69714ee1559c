// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:rpg_game/main.dart';

void main() {
  testWidgets('RPG Game app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const RPGGameApp());

    // Verify that the main menu is displayed
    expect(find.text('RPG ADVENTURE'), findsOneWidget);
    expect(find.text('New Adventure'), findsOneWidget);
    expect(find.text('Continue Journey'), findsOneWidget);
    expect(find.text('Hall of Fame'), findsOneWidget);
  });
}
