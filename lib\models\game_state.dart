import 'player.dart';
import 'alien.dart';
import 'bullet.dart';

enum GameStatus { menu, playing, paused, gameOver, levelComplete }

class GameState {
  GameStatus status;
  Player player;
  List<Alien> aliens;
  List<Bullet> playerBullets;
  List<Bullet> alienBullets;
  int score;
  int level;
  double gameTime;
  bool isMovingRight;
  double alienMoveTimer;
  double alienMoveInterval;
  double alienDropDistance;

  GameState({
    this.status = GameStatus.menu,
    required this.player,
    List<Alien>? aliens,
    List<Bullet>? playerBullets,
    List<Bullet>? alienBullets,
    this.score = 0,
    this.level = 1,
    this.gameTime = 0.0,
    this.isMovingRight = true,
    this.alienMoveTimer = 0.0,
    this.alienMoveInterval = 1.0,
    this.alienDropDistance = 20.0,
  }) : 
    aliens = aliens ?? [],
    playerBullets = playerBullets ?? [],
    alienBullets = alienBullets ?? [];

  void reset() {
    status = GameStatus.playing;
    score = 0;
    level = 1;
    gameTime = 0.0;
    isMovingRight = true;
    alienMoveTimer = 0.0;
    alienMoveInterval = 1.0;
    
    // Reset player
    player.lives = 3;
    player.x = 200; // Will be adjusted based on screen size
    player.canShoot = true;
    player.currentCooldown = 0.0;
    
    // Clear all bullets
    playerBullets.clear();
    alienBullets.clear();
    
    // Clear aliens (will be repopulated by game engine)
    aliens.clear();
  }

  void nextLevel() {
    level++;
    status = GameStatus.playing;
    
    // Increase difficulty
    alienMoveInterval = (alienMoveInterval * 0.9).clamp(0.2, 1.0);
    
    // Clear bullets
    playerBullets.clear();
    alienBullets.clear();
    
    // Clear aliens (will be repopulated by game engine)
    aliens.clear();
  }

  void addScore(int points) {
    score += points;
  }

  bool isGameOver() {
    return !player.isAlive() || aliens.any((alien) => alien.y + alien.height >= player.y);
  }

  bool isLevelComplete() {
    return aliens.isEmpty;
  }

  void pause() {
    if (status == GameStatus.playing) {
      status = GameStatus.paused;
    }
  }

  void resume() {
    if (status == GameStatus.paused) {
      status = GameStatus.playing;
    }
  }

  void gameOver() {
    status = GameStatus.gameOver;
  }

  GameState copyWith({
    GameStatus? status,
    Player? player,
    List<Alien>? aliens,
    List<Bullet>? playerBullets,
    List<Bullet>? alienBullets,
    int? score,
    int? level,
    double? gameTime,
    bool? isMovingRight,
    double? alienMoveTimer,
    double? alienMoveInterval,
    double? alienDropDistance,
  }) {
    return GameState(
      status: status ?? this.status,
      player: player ?? this.player,
      aliens: aliens ?? this.aliens,
      playerBullets: playerBullets ?? this.playerBullets,
      alienBullets: alienBullets ?? this.alienBullets,
      score: score ?? this.score,
      level: level ?? this.level,
      gameTime: gameTime ?? this.gameTime,
      isMovingRight: isMovingRight ?? this.isMovingRight,
      alienMoveTimer: alienMoveTimer ?? this.alienMoveTimer,
      alienMoveInterval: alienMoveInterval ?? this.alienMoveInterval,
      alienDropDistance: alienDropDistance ?? this.alienDropDistance,
    );
  }
}
