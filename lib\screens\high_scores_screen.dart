import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../models/high_score.dart';

class HighScoresScreen extends StatefulWidget {
  const HighScoresScreen({Key? key}) : super(key: key);

  @override
  State<HighScoresScreen> createState() => _HighScoresScreenState();
}

class _HighScoresScreenState extends State<HighScoresScreen> {
  List<HighScore> _highScores = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadHighScores();
  }

  Future<void> _loadHighScores() async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final scores = await gameProvider.getHighScores();
    
    setState(() {
      _highScores = scores;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF000033),
              Color(0xFF000011),
              Color(0xFF000000),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // High Scores List
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildHighScoresList(),
              ),
              
              // Back Button
              _buildBackButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              colors: [Colors.yellow, Colors.orange, Colors.red],
            ).createShader(bounds),
            child: const Text(
              'HIGH SCORES',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: 4,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 2,
            width: 200,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.transparent, Colors.orange, Colors.transparent],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighScoresList() {
    if (_highScores.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No high scores yet!',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Play the game to set your first record.',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: _highScores.length,
      itemBuilder: (context, index) {
        final score = _highScores[index];
        final rank = index + 1;
        
        return _buildScoreItem(score, rank);
      },
    );
  }

  Widget _buildScoreItem(HighScore score, int rank) {
    Color rankColor;
    IconData rankIcon;
    
    switch (rank) {
      case 1:
        rankColor = Colors.yellow;
        rankIcon = Icons.emoji_events;
        break;
      case 2:
        rankColor = Colors.grey[300]!;
        rankIcon = Icons.emoji_events;
        break;
      case 3:
        rankColor = Colors.orange[300]!;
        rankIcon = Icons.emoji_events;
        break;
      default:
        rankColor = Colors.cyan;
        rankIcon = Icons.star;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        border: Border.all(
          color: rank <= 3 ? rankColor : Colors.grey[700]!,
          width: rank <= 3 ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: rank <= 3
            ? [
                BoxShadow(
                  color: rankColor.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ]
            : null,
      ),
      child: Row(
        children: [
          // Rank
          Container(
            width: 50,
            child: Row(
              children: [
                Icon(
                  rankIcon,
                  color: rankColor,
                  size: rank <= 3 ? 24 : 20,
                ),
                const SizedBox(width: 4),
                Text(
                  '$rank',
                  style: TextStyle(
                    color: rankColor,
                    fontSize: rank <= 3 ? 18 : 16,
                    fontWeight: rank <= 3 ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
          
          // Player Name
          Expanded(
            flex: 2,
            child: Text(
              score.playerName,
              style: TextStyle(
                color: Colors.white,
                fontSize: rank <= 3 ? 18 : 16,
                fontWeight: rank <= 3 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          
          // Score
          Expanded(
            flex: 2,
            child: Text(
              score.score.toString().padLeft(6, '0'),
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Colors.green,
                fontSize: rank <= 3 ? 18 : 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
          ),
          
          // Date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(score.date),
              textAlign: TextAlign.right,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildBackButton() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: SizedBox(
        width: 200,
        child: ElevatedButton(
          onPressed: () => Navigator.pop(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.withOpacity(0.2),
            foregroundColor: Colors.blue,
            side: const BorderSide(color: Colors.blue, width: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.arrow_back),
              SizedBox(width: 8),
              Text('BACK TO MENU'),
            ],
          ),
        ),
      ),
    );
  }
}
