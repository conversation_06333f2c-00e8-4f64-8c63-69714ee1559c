import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../models/character.dart';
import '../models/item.dart';

class CharacterScreen extends StatefulWidget {
  const CharacterScreen({super.key});

  @override
  State<CharacterScreen> createState() => _CharacterScreenState();
}

class _CharacterScreenState extends State<CharacterScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Character'),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: Consumer<GameProvider>(
          builder: (context, gameProvider, child) {
            final character = gameProvider.currentCharacter;

            if (character == null) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildCharacterHeader(character),
                  const SizedBox(height: 20),
                  _buildStatsCard(character),
                  const SizedBox(height: 20),
                  _buildEquipmentCard(character, gameProvider),
                  const SizedBox(height: 20),
                  _buildProgressCard(character),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCharacterHeader(Character character) {
    return Card(
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.amber, width: 4),
                gradient: const LinearGradient(
                  colors: [Colors.amber, Colors.orange],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.amber.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Icon(
                Icons.person,
                size: 50,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              character.name,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.amber),
              ),
              child: Text(
                'Level ${character.baseStats.level} Adventurer',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(Character character) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Character Stats',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Health',
                    '${character.baseStats.currentHp}/${character.totalStats.maxHp}',
                    Icons.favorite,
                    Colors.red,
                    character.baseStats.currentHp / character.totalStats.maxHp,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    'Attack',
                    '${character.totalStats.attack}',
                    Icons.local_fire_department,
                    Colors.orange,
                    null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Defense',
                    '${character.totalStats.defense}',
                    Icons.shield,
                    Colors.blue,
                    null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    'Speed',
                    '${character.totalStats.speed}',
                    Icons.speed,
                    Colors.green,
                    null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              'Gold',
              '${character.baseStats.gold}',
              Icons.monetization_on,
              Colors.yellow,
              null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color, double? progress) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (progress != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 4,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEquipmentCard(Character character, GameProvider gameProvider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Equipment',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            _buildEquipmentSlot(
              'Weapon',
              character.equipment.weapon,
              Icons.local_fire_department,
              Colors.orange,
              () => _showEquipmentDialog(context, ItemType.weapon, gameProvider),
            ),
            const SizedBox(height: 12),
            _buildEquipmentSlot(
              'Armor',
              character.equipment.armor,
              Icons.shield,
              Colors.blue,
              () => _showEquipmentDialog(context, ItemType.armor, gameProvider),
            ),
            const SizedBox(height: 12),
            _buildEquipmentSlot(
              'Accessory',
              character.equipment.accessory,
              Icons.diamond,
              Colors.purple,
              () => _showEquipmentDialog(context, ItemType.accessory, gameProvider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEquipmentSlot(String slotName, Item? item, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    slotName,
                    style: TextStyle(
                      color: color,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item?.name ?? 'Empty',
                    style: TextStyle(
                      color: item != null ? Colors.white : Colors.white54,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (item != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      _getItemStatsText(item),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              item != null ? Icons.swap_horiz : Icons.add,
              color: Colors.white54,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard(Character character) {
    final expProgress = character.baseStats.experience / character.baseStats.experienceToNext;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Progress',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Experience',
                  style: TextStyle(
                    color: Colors.white70,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${character.baseStats.experience}/${character.baseStats.experienceToNext}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: expProgress,
              backgroundColor: Colors.blue.withOpacity(0.3),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
              minHeight: 8,
            ),
            const SizedBox(height: 16),
            if (character.baseStats.canLevelUp())
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.star, color: Colors.green),
                    SizedBox(width: 8),
                    Text(
                      'Ready to level up!',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getItemStatsText(Item item) {
    final stats = <String>[];
    if (item.attackBonus > 0) stats.add('+${item.attackBonus} ATK');
    if (item.defenseBonus > 0) stats.add('+${item.defenseBonus} DEF');
    if (item.speedBonus > 0) stats.add('+${item.speedBonus} SPD');
    if (item.hpBonus > 0) stats.add('+${item.hpBonus} HP');
    return stats.join(', ');
  }

  void _showEquipmentDialog(BuildContext context, ItemType type, GameProvider gameProvider) {
    final availableItems = gameProvider.inventory.getItemsByType(type);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text('Equip ${type.name.toUpperCase()}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: availableItems.isEmpty
              ? const Center(
                  child: Text(
                    'No items available',
                    style: TextStyle(color: Colors.white70),
                  ),
                )
              : ListView.builder(
                  itemCount: availableItems.length,
                  itemBuilder: (context, index) {
                    final slot = availableItems[index];
                    return ListTile(
                      title: Text(
                        slot.item.name,
                        style: const TextStyle(color: Colors.white),
                      ),
                      subtitle: Text(
                        _getItemStatsText(slot.item),
                        style: const TextStyle(color: Colors.white70),
                      ),
                      onTap: () {
                        gameProvider.equipItem(slot.item);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          if (gameProvider.currentCharacter != null)
            TextButton(
              onPressed: () {
                gameProvider.unequipItem(type);
                Navigator.pop(context);
              },
              child: const Text('Unequip'),
            ),
        ],
      ),
    );
  }
}
