import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/business_provider.dart';
import '../models/business.dart';
import '../models/employee.dart';
import '../models/product.dart';
import '../models/market.dart';
import 'main_menu_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Load market data when dashboard opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BusinessProvider>().loadMarket();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BusinessProvider>(
      builder: (context, businessProvider, child) {
        final business = businessProvider.currentBusiness;

        if (business == null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 80,
                    color: Colors.white54,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No Business Loaded',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(builder: (context) => const MainMenuScreen()),
                      );
                    },
                    child: const Text('Back to Menu'),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(business.name),
            centerTitle: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.save),
                onPressed: () => businessProvider.saveCurrentBusiness(),
                tooltip: 'Save Progress',
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'menu') {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(builder: (context) => const MainMenuScreen()),
                    );
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'menu',
                    child: Row(
                      children: [
                        Icon(Icons.home),
                        SizedBox(width: 8),
                        Text('Main Menu'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF0D1421),
                  Color(0xFF1E2A3A),
                ],
              ),
            ),
            child: _buildDashboardContent(business, businessProvider),
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) => setState(() => _selectedIndex = index),
            type: BottomNavigationBarType.fixed,
            backgroundColor: const Color(0xFF1E2A3A),
            selectedItemColor: const Color(0xFF2196F3),
            unselectedItemColor: Colors.white54,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.dashboard),
                label: 'Dashboard',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.people),
                label: 'Employees',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.inventory),
                label: 'Products',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.trending_up),
                label: 'Market',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDashboardContent(Business business, BusinessProvider provider) {
    switch (_selectedIndex) {
      case 0:
        return _buildOverviewTab(business, provider);
      case 1:
        return _buildEmployeesTab(business, provider);
      case 2:
        return _buildProductsTab(business, provider);
      case 3:
        return _buildMarketTab(business, provider);
      default:
        return _buildOverviewTab(business, provider);
    }
  }

  Widget _buildOverviewTab(Business business, BusinessProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFinancialSummary(business),
          const SizedBox(height: 16),
          _buildBusinessStats(business),
          const SizedBox(height: 16),
          _buildQuickActions(business, provider),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary(Business business) {
    return Card(
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Financial Overview',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildFinancialItem(
                    'Cash',
                    '\$${_formatNumber(business.cash)}',
                    Icons.monetization_on,
                    business.cash >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildFinancialItem(
                    'Valuation',
                    '\$${_formatNumber(business.valuation)}',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildFinancialItem(
                    'Monthly Profit',
                    '\$${_formatNumber(business.monthlyProfit)}',
                    Icons.account_balance,
                    business.monthlyProfit >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildFinancialItem(
                    'Reputation',
                    '${business.reputation.toStringAsFixed(1)}/100',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessStats(Business business) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Business Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Industry', business.industryName),
            _buildStatRow('Level', '${business.level}'),
            _buildStatRow('Employees', '${business.employees.length}'),
            _buildStatRow('Products', '${business.products.length}'),
            _buildStatRow('Cities', '${business.unlockedCities.length}'),
            _buildStatRow('Founded', _formatDate(business.foundedDate)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(Business business, BusinessProvider provider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Take Loan',
                    Icons.account_balance,
                    Colors.orange,
                    () => _showLoanDialog(provider),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    'Marketing',
                    Icons.campaign,
                    Colors.purple,
                    () => _showMarketingDialog(provider),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeesTab(Business business, BusinessProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildEmployeeStats(business),
          const SizedBox(height: 16),
          _buildHireButton(provider),
          const SizedBox(height: 16),
          _buildEmployeeList(business, provider),
        ],
      ),
    );
  }

  Widget _buildEmployeeStats(Business business) {
    final totalSalaries = business.totalMonthlySalaries;
    final avgSatisfaction = business.employees.isEmpty
        ? 0.0
        : business.employees.fold(0.0, (sum, emp) => sum + emp.satisfaction) / business.employees.length;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Employee Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildEmployeeStatItem(
                    'Total Employees',
                    '${business.employees.length}',
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildEmployeeStatItem(
                    'Monthly Salaries',
                    '\$${_formatNumber(totalSalaries)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildEmployeeStatItem(
                    'Avg Satisfaction',
                    '${avgSatisfaction.toStringAsFixed(1)}%',
                    Icons.sentiment_satisfied,
                    avgSatisfaction >= 75 ? Colors.green : avgSatisfaction >= 50 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildEmployeeStatItem(
                    'Productivity',
                    '${_calculateTotalProductivity(business).toStringAsFixed(1)}',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  double _calculateTotalProductivity(Business business) {
    return business.employees.fold(0.0, (sum, emp) => sum + emp.effectiveProductivity);
  }

  Widget _buildHireButton(BusinessProvider provider) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: () => _showHireDialog(provider),
        icon: const Icon(Icons.person_add),
        label: const Text('Hire New Employee'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeList(Business business, BusinessProvider provider) {
    if (business.employees.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(40),
          child: Column(
            children: [
              Icon(
                Icons.people_outline,
                size: 60,
                color: Colors.white.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No Employees Yet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Hire your first employee to start growing your business!',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Team',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...business.employees.map((employee) => _buildEmployeeCard(employee, provider)),
      ],
    );
  }

  Widget _buildEmployeeCard(Employee employee, BusinessProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getRoleColor(employee.role).withOpacity(0.2),
                  ),
                  child: Icon(
                    _getRoleIcon(employee.role),
                    color: _getRoleColor(employee.role),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        employee.fullTitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: _getRoleColor(employee.role),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'fire') {
                      _showFireConfirmation(employee, provider);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'fire',
                      child: Row(
                        children: [
                          Icon(Icons.person_remove, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Fire Employee'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildEmployeeDetailItem(
                    'Salary',
                    '\$${employee.salary.toStringAsFixed(0)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildEmployeeDetailItem(
                    'Satisfaction',
                    '${employee.satisfaction.toStringAsFixed(1)}%',
                    Icons.sentiment_satisfied,
                    employee.satisfaction >= 75 ? Colors.green : employee.satisfaction >= 50 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildEmployeeDetailItem(
                    'Productivity',
                    '${employee.effectiveProductivity.toStringAsFixed(1)}',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeDetailItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsTab(Business business, BusinessProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildProductStats(business),
          const SizedBox(height: 16),
          _buildLaunchProductButton(provider),
          const SizedBox(height: 16),
          _buildProductList(business, provider),
        ],
      ),
    );
  }

  Widget _buildProductStats(Business business) {
    final totalStock = business.products.fold(0, (sum, product) => sum + product.stock);
    final totalRevenue = business.products.fold(0.0, (sum, product) => sum + product.totalProfit);
    final avgQuality = business.products.isEmpty
        ? 0.0
        : business.products.fold(0.0, (sum, product) => sum + product.quality) / business.products.length;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Product Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildProductStatItem(
                    'Total Products',
                    '${business.products.length}',
                    Icons.inventory,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildProductStatItem(
                    'Total Stock',
                    '$totalStock',
                    Icons.warehouse,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildProductStatItem(
                    'Total Revenue',
                    '\$${_formatNumber(totalRevenue)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildProductStatItem(
                    'Avg Quality',
                    '${avgQuality.toStringAsFixed(1)}',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLaunchProductButton(BusinessProvider provider) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: () => _showLaunchProductDialog(provider),
        icon: const Icon(Icons.add_box),
        label: const Text('Launch New Product'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2196F3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildProductList(Business business, BusinessProvider provider) {
    if (business.products.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(40),
          child: Column(
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 60,
                color: Colors.white.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No Products Yet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Launch your first product to start generating revenue!',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Products',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...business.products.map((product) => _buildProductCard(product, provider)),
      ],
    );
  }

  Widget _buildProductCard(Product product, BusinessProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getCategoryColor(product.category).withOpacity(0.2),
                  ),
                  child: Icon(
                    _getCategoryIcon(product.category),
                    color: _getCategoryColor(product.category),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.categoryName,
                        style: TextStyle(
                          fontSize: 14,
                          color: _getCategoryColor(product.category),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'restock') {
                      _showRestockDialog(product, provider);
                    } else if (value == 'price') {
                      _showPriceDialog(product, provider);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'restock',
                      child: Row(
                        children: [
                          Icon(Icons.add_box, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('Restock'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'price',
                      child: Row(
                        children: [
                          Icon(Icons.price_change, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('Adjust Price'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildProductDetailItem(
                    'Price',
                    '\$${product.sellingPrice.toStringAsFixed(2)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildProductDetailItem(
                    'Stock',
                    '${product.stock}',
                    Icons.inventory,
                    product.stock > 10 ? Colors.green : product.stock > 0 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildProductDetailItem(
                    'Sold',
                    '${product.unitsSold}',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Profit/Unit: \$${product.profitPerUnit.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: product.profitPerUnit > 0 ? Colors.green : Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Total Profit: \$${product.totalProfit.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketTab(Business business, BusinessProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildMarketConditions(provider.market),
          const SizedBox(height: 16),
          _buildIndustryTrends(business, provider.market),
          const SizedBox(height: 16),
          _buildMarketActions(business, provider),
        ],
      ),
    );
  }

  Widget _buildMarketConditions(Market market) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Market Conditions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getMarketConditionColor(market.condition).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getMarketConditionColor(market.condition).withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        _getMarketConditionIcon(market.condition),
                        color: _getMarketConditionColor(market.condition),
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              market.conditionName,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: _getMarketConditionColor(market.condition),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              market.conditionDescription,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildMarketStatItem(
                          'Demand',
                          '${(market.demandMultiplier * 100).toStringAsFixed(0)}%',
                          Icons.trending_up,
                          market.demandMultiplier >= 1.0 ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildMarketStatItem(
                          'Costs',
                          '${(market.costMultiplier * 100).toStringAsFixed(0)}%',
                          Icons.monetization_on,
                          market.costMultiplier <= 1.0 ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildMarketStatItem(
                          'Inflation',
                          '${(market.inflationRate * 100).toStringAsFixed(1)}%',
                          Icons.show_chart,
                          market.inflationRate <= 0.03 ? Colors.green : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndustryTrends(Business business, Market market) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Industry Trends',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            ...market.industryTrends.entries.map((entry) {
              final isMyIndustry = entry.key == business.industry.toString().split('.').last;
              final trend = entry.value;
              final color = trend >= 1.0 ? Colors.green : Colors.red;

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isMyIndustry ? color.withOpacity(0.2) : Colors.white.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isMyIndustry ? color.withOpacity(0.5) : Colors.white.withOpacity(0.1),
                    width: isMyIndustry ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getIndustryIcon(entry.key),
                      color: isMyIndustry ? color : Colors.white70,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.key.toUpperCase(),
                        style: TextStyle(
                          color: isMyIndustry ? Colors.white : Colors.white70,
                          fontSize: 14,
                          fontWeight: isMyIndustry ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${(trend * 100).toStringAsFixed(0)}%',
                        style: TextStyle(
                          color: color,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      trend >= 1.0 ? Icons.trending_up : Icons.trending_down,
                      color: color,
                      size: 16,
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketActions(Business business, BusinessProvider provider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Market Analysis',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.analytics, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Your Industry: ${business.industryName}',
                        style: const TextStyle(
                          color: Colors.blue,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _getIndustryAnalysis(business, provider.market),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildAnalysisItem(
                          'Industry Bonus',
                          '${(business.industryBonus * 100).toStringAsFixed(0)}%',
                          Icons.star,
                          Colors.amber,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildAnalysisItem(
                          'Market Effect',
                          '${(provider.market.getIndustryMultiplier(business.industry.toString()) * 100).toStringAsFixed(0)}%',
                          Icons.show_chart,
                          provider.market.getIndustryMultiplier(business.industry.toString()) >= 1.0 ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatNumber(double number) {
    if (number >= **********) {
      return '${(number / **********).toStringAsFixed(1)}B';
    } else if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toStringAsFixed(0);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showLoanDialog(BusinessProvider provider) {
    final business = provider.currentBusiness!;
    double loanAmount = 10000;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: const Text('Take Out Loan'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current Cash: \$${_formatNumber(business.cash)}',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              Text(
                'Loan Amount: \$${_formatNumber(loanAmount)}',
                style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Slider(
                value: loanAmount,
                min: 5000,
                max: 100000,
                divisions: 19,
                onChanged: (value) => setState(() => loanAmount = value),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Text(
                      'Interest Rate: 10% annually',
                      style: TextStyle(color: Colors.orange, fontSize: 12),
                    ),
                    Text(
                      'Monthly Payment: \$${_formatNumber(loanAmount * 0.1 / 12)}',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                provider.takeOutLoan(loanAmount);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Loan of \$${_formatNumber(loanAmount)} approved!')),
                );
              },
              child: const Text('Take Loan'),
            ),
          ],
        ),
      ),
    );
  }

  void _showMarketingDialog(BusinessProvider provider) {
    final business = provider.currentBusiness!;
    double marketingAmount = 1000;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: const Text('Marketing Campaign'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current Cash: \$${_formatNumber(business.cash)}',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 8),
              Text(
                'Current Reputation: ${business.reputation.toStringAsFixed(1)}/100',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              Text(
                'Investment: \$${_formatNumber(marketingAmount)}',
                style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Slider(
                value: marketingAmount,
                min: 500,
                max: business.cash.clamp(500, 50000),
                divisions: 20,
                onChanged: (value) => setState(() => marketingAmount = value),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.purple.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Text(
                      'Expected Reputation Boost: +${(marketingAmount / 1000).toStringAsFixed(1)}',
                      style: TextStyle(color: Colors.purple, fontSize: 12),
                    ),
                    Text(
                      'Increased customer awareness and sales',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: business.cash >= marketingAmount ? () {
                provider.investInMarketing(marketingAmount);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Marketing campaign launched for \$${_formatNumber(marketingAmount)}!')),
                );
              } : null,
              child: const Text('Launch Campaign'),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for employee management
  Color _getRoleColor(EmployeeRole role) {
    switch (role) {
      case EmployeeRole.sales:
        return Colors.green;
      case EmployeeRole.engineer:
        return Colors.blue;
      case EmployeeRole.manager:
        return Colors.purple;
      case EmployeeRole.marketing:
        return Colors.orange;
      case EmployeeRole.finance:
        return Colors.teal;
      case EmployeeRole.hr:
        return Colors.pink;
    }
  }

  IconData _getRoleIcon(EmployeeRole role) {
    switch (role) {
      case EmployeeRole.sales:
        return Icons.trending_up;
      case EmployeeRole.engineer:
        return Icons.engineering;
      case EmployeeRole.manager:
        return Icons.supervisor_account;
      case EmployeeRole.marketing:
        return Icons.campaign;
      case EmployeeRole.finance:
        return Icons.account_balance;
      case EmployeeRole.hr:
        return Icons.people;
    }
  }

  void _showHireDialog(BusinessProvider provider) {
    final availableEmployees = EmployeeTemplates.getAvailableEmployees();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Hire New Employee'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: availableEmployees.length,
            itemBuilder: (context, index) {
              final employee = availableEmployees[index];
              final hiringCost = employee.salary * 3; // 3 months upfront
              final canAfford = provider.currentBusiness!.cash >= hiringCost;

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getRoleColor(employee.role).withOpacity(0.2),
                    ),
                    child: Icon(
                      _getRoleIcon(employee.role),
                      color: _getRoleColor(employee.role),
                      size: 20,
                    ),
                  ),
                  title: Text(
                    employee.name,
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.fullTitle,
                        style: TextStyle(color: _getRoleColor(employee.role)),
                      ),
                      Text(
                        'Salary: \$${employee.salary.toStringAsFixed(0)}/month',
                        style: const TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                      Text(
                        'Hiring Cost: \$${hiringCost.toStringAsFixed(0)} (3 months)',
                        style: TextStyle(
                          color: canAfford ? Colors.green : Colors.red,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  trailing: ElevatedButton(
                    onPressed: canAfford ? () {
                      provider.hireEmployee(employee);
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${employee.name} has been hired!')),
                      );
                    } : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Hire'),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFireConfirmation(Employee employee, BusinessProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Fire Employee'),
        content: Text(
          'Are you sure you want to fire ${employee.name}?\n\n'
          'This will immediately remove them from your team and stop their salary payments.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.fireEmployee(employee.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${employee.name} has been fired.')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Fire'),
          ),
        ],
      ),
    );
  }

  // Helper methods for product management
  Color _getCategoryColor(ProductCategory category) {
    switch (category) {
      case ProductCategory.software:
        return Colors.blue;
      case ProductCategory.hardware:
        return Colors.orange;
      case ProductCategory.service:
        return Colors.purple;
      case ProductCategory.food:
        return Colors.red;
      case ProductCategory.clothing:
        return Colors.pink;
      case ProductCategory.electronics:
        return Colors.teal;
    }
  }

  IconData _getCategoryIcon(ProductCategory category) {
    switch (category) {
      case ProductCategory.software:
        return Icons.computer;
      case ProductCategory.hardware:
        return Icons.memory;
      case ProductCategory.service:
        return Icons.handyman;
      case ProductCategory.food:
        return Icons.restaurant;
      case ProductCategory.clothing:
        return Icons.checkroom;
      case ProductCategory.electronics:
        return Icons.electrical_services;
    }
  }

  Widget _buildProductDetailItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showLaunchProductDialog(BusinessProvider provider) {
    final business = provider.currentBusiness!;
    List<Product> availableProducts;

    switch (business.industry) {
      case Industry.technology:
        availableProducts = ProductTemplates.getTechnologyProducts();
        break;
      case Industry.retail:
        availableProducts = ProductTemplates.getRetailProducts();
        break;
      case Industry.manufacturing:
        availableProducts = ProductTemplates.getManufacturingProducts();
        break;
      case Industry.food:
        availableProducts = ProductTemplates.getFoodProducts();
        break;
      case Industry.healthcare:
        availableProducts = []; // Add healthcare products if needed
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Launch New Product'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: availableProducts.isEmpty
              ? const Center(
                  child: Text(
                    'No products available for your industry yet.',
                    style: TextStyle(color: Colors.white70),
                  ),
                )
              : ListView.builder(
                  itemCount: availableProducts.length,
                  itemBuilder: (context, index) {
                    final product = availableProducts[index];
                    final developmentCost = product.productionCost * 10; // Development cost
                    final canAfford = business.cash >= developmentCost;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _getCategoryColor(product.category).withOpacity(0.2),
                          ),
                          child: Icon(
                            _getCategoryIcon(product.category),
                            color: _getCategoryColor(product.category),
                            size: 20,
                          ),
                        ),
                        title: Text(
                          product.name,
                          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.categoryName,
                              style: TextStyle(color: _getCategoryColor(product.category)),
                            ),
                            Text(
                              'Production Cost: \$${product.productionCost.toStringAsFixed(2)}/unit',
                              style: const TextStyle(color: Colors.white70, fontSize: 12),
                            ),
                            Text(
                              'Development Cost: \$${developmentCost.toStringAsFixed(0)}',
                              style: TextStyle(
                                color: canAfford ? Colors.green : Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        trailing: ElevatedButton(
                          onPressed: canAfford ? () {
                            business.cash -= developmentCost;
                            provider.launchProduct(product);
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('${product.name} has been launched!')),
                            );
                          } : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF2196F3),
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Launch'),
                        ),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRestockDialog(Product product, BusinessProvider provider) {
    int quantity = 10;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: Text('Restock ${product.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current Stock: ${product.stock}',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              Text(
                'Quantity: $quantity',
                style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Slider(
                value: quantity.toDouble(),
                min: 1,
                max: 100,
                divisions: 99,
                onChanged: (value) => setState(() => quantity = value.round()),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Text(
                      'Total Cost: \$${(product.productionCost * quantity).toStringAsFixed(2)}',
                      style: const TextStyle(color: Colors.blue, fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Cost per unit: \$${product.productionCost.toStringAsFixed(2)}',
                      style: const TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: provider.currentBusiness!.cash >= (product.productionCost * quantity) ? () {
                provider.restockProduct(product.id, quantity);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${product.name} restocked with $quantity units!')),
                );
              } : null,
              child: const Text('Restock'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPriceDialog(Product product, BusinessProvider provider) {
    double newPrice = product.sellingPrice;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: Text('Adjust Price - ${product.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current Price: \$${product.sellingPrice.toStringAsFixed(2)}',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 8),
              Text(
                'Production Cost: \$${product.productionCost.toStringAsFixed(2)}',
                style: const TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              Text(
                'New Price: \$${newPrice.toStringAsFixed(2)}',
                style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Slider(
                value: newPrice,
                min: product.productionCost * 0.5,
                max: product.productionCost * 10,
                divisions: 100,
                onChanged: (value) => setState(() => newPrice = value),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Text(
                      'Profit per unit: \$${(newPrice - product.productionCost).toStringAsFixed(2)}',
                      style: TextStyle(
                        color: newPrice > product.productionCost ? Colors.green : Colors.red,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Markup: ${((newPrice / product.productionCost - 1) * 100).toStringAsFixed(1)}%',
                      style: const TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                provider.adjustProductPrice(product.id, newPrice);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${product.name} price updated to \$${newPrice.toStringAsFixed(2)}!')),
                );
              },
              child: const Text('Update Price'),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for market functionality
  Color _getMarketConditionColor(MarketCondition condition) {
    switch (condition) {
      case MarketCondition.recession:
        return Colors.red;
      case MarketCondition.slow:
        return Colors.orange;
      case MarketCondition.normal:
        return Colors.blue;
      case MarketCondition.growth:
        return Colors.green;
      case MarketCondition.boom:
        return Colors.purple;
    }
  }

  IconData _getMarketConditionIcon(MarketCondition condition) {
    switch (condition) {
      case MarketCondition.recession:
        return Icons.trending_down;
      case MarketCondition.slow:
        return Icons.trending_flat;
      case MarketCondition.normal:
        return Icons.show_chart;
      case MarketCondition.growth:
        return Icons.trending_up;
      case MarketCondition.boom:
        return Icons.rocket_launch;
    }
  }

  IconData _getIndustryIcon(String industry) {
    switch (industry.toLowerCase()) {
      case 'technology':
        return Icons.computer;
      case 'retail':
        return Icons.shopping_bag;
      case 'manufacturing':
        return Icons.precision_manufacturing;
      case 'food':
        return Icons.restaurant;
      case 'healthcare':
        return Icons.local_hospital;
      default:
        return Icons.business;
    }
  }

  String _getIndustryAnalysis(Business business, Market market) {
    final industryMultiplier = market.getIndustryMultiplier(business.industry.toString());
    final marketCondition = market.condition;

    String analysis = '';

    if (industryMultiplier >= 1.2) {
      analysis += 'Your industry is experiencing strong growth! ';
    } else if (industryMultiplier >= 1.0) {
      analysis += 'Your industry is performing well. ';
    } else if (industryMultiplier >= 0.8) {
      analysis += 'Your industry is facing some challenges. ';
    } else {
      analysis += 'Your industry is in a difficult period. ';
    }

    switch (marketCondition) {
      case MarketCondition.recession:
        analysis += 'The overall economy is in recession, affecting all businesses.';
        break;
      case MarketCondition.slow:
        analysis += 'Economic growth is slow but steady.';
        break;
      case MarketCondition.normal:
        analysis += 'Market conditions are stable and balanced.';
        break;
      case MarketCondition.growth:
        analysis += 'The economy is growing, creating new opportunities.';
        break;
      case MarketCondition.boom:
        analysis += 'The economy is booming with high demand across sectors!';
        break;
    }

    return analysis;
  }
}
