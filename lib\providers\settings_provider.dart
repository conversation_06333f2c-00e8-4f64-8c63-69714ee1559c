import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../models/game_settings.dart';

class SettingsProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  GameSettings _settings = GameSettings();

  GameSettings get settings => _settings;
  bool get soundEnabled => _settings.soundEnabled;
  bool get musicEnabled => _settings.musicEnabled;
  double get soundVolume => _settings.soundVolume;
  double get musicVolume => _settings.musicVolume;
  String get controlType => _settings.controlType;

  Future<void> loadSettings() async {
    try {
      _settings = await _databaseHelper.getSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Use default settings if loading fails
      _settings = GameSettings();
      notifyListeners();
    }
  }

  Future<void> updateSoundEnabled(bool enabled) async {
    _settings = _settings.copyWith(soundEnabled: enabled);
    await _saveSettings();
    notifyListeners();
  }

  Future<void> updateMusicEnabled(bool enabled) async {
    _settings = _settings.copyWith(musicEnabled: enabled);
    await _saveSettings();
    notifyListeners();
  }

  Future<void> updateSoundVolume(double volume) async {
    _settings = _settings.copyWith(soundVolume: volume.clamp(0.0, 1.0));
    await _saveSettings();
    notifyListeners();
  }

  Future<void> updateMusicVolume(double volume) async {
    _settings = _settings.copyWith(musicVolume: volume.clamp(0.0, 1.0));
    await _saveSettings();
    notifyListeners();
  }

  Future<void> updateControlType(String controlType) async {
    if (controlType == 'touch' || controlType == 'buttons') {
      _settings = _settings.copyWith(controlType: controlType);
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> resetToDefaults() async {
    _settings = GameSettings();
    await _saveSettings();
    notifyListeners();
  }

  Future<void> _saveSettings() async {
    try {
      if (_settings.id != null) {
        await _databaseHelper.updateSettings(_settings);
      } else {
        final id = await _databaseHelper.insertSettings(_settings);
        _settings = _settings.copyWith(id: id);
      }
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }
}
