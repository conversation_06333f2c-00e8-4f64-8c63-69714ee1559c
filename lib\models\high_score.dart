class HighScore {
  final int? id;
  final String playerName;
  final int score;
  final DateTime date;

  HighScore({
    this.id,
    required this.playerName,
    required this.score,
    required this.date,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'playerName': playerName,
      'score': score,
      'date': date.millisecondsSinceEpoch,
    };
  }

  factory HighScore.fromMap(Map<String, dynamic> map) {
    return HighScore(
      id: map['id'],
      playerName: map['playerName'],
      score: map['score'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
    );
  }

  @override
  String toString() {
    return 'HighScore{id: $id, playerName: $playerName, score: $score, date: $date}';
  }
}
