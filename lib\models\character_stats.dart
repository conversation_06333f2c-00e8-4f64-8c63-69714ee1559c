class CharacterStats {
  final int level;
  final int experience;
  final int experienceToNext;
  final int maxHp;
  final int currentHp;
  final int attack;
  final int defense;
  final int speed;
  final int gold;

  CharacterStats({
    this.level = 1,
    this.experience = 0,
    this.experienceToNext = 100,
    this.maxHp = 100,
    this.currentHp = 100,
    this.attack = 10,
    this.defense = 5,
    this.speed = 8,
    this.gold = 0,
  });

  CharacterStats copyWith({
    int? level,
    int? experience,
    int? experienceToNext,
    int? maxHp,
    int? currentHp,
    int? attack,
    int? defense,
    int? speed,
    int? gold,
  }) {
    return CharacterStats(
      level: level ?? this.level,
      experience: experience ?? this.experience,
      experienceToNext: experienceToNext ?? this.experienceToNext,
      maxHp: maxHp ?? this.maxHp,
      currentHp: currentHp ?? this.currentHp,
      attack: attack ?? this.attack,
      defense: defense ?? this.defense,
      speed: speed ?? this.speed,
      gold: gold ?? this.gold,
    );
  }

  // Calculate total stats including equipment bonuses
  CharacterStats withEquipmentBonuses({
    int attackBonus = 0,
    int defenseBonus = 0,
    int speedBonus = 0,
    int hpBonus = 0,
  }) {
    return copyWith(
      attack: attack + attackBonus,
      defense: defense + defenseBonus,
      speed: speed + speedBonus,
      maxHp: maxHp + hpBonus,
    );
  }

  // Level up calculations
  CharacterStats levelUp() {
    final newLevel = level + 1;
    final hpIncrease = 15 + (newLevel * 2);
    final attackIncrease = 2 + (newLevel ~/ 3);
    final defenseIncrease = 1 + (newLevel ~/ 4);
    final speedIncrease = newLevel % 2 == 0 ? 1 : 0;
    
    return copyWith(
      level: newLevel,
      experience: experience - experienceToNext,
      experienceToNext: _calculateExpToNext(newLevel),
      maxHp: maxHp + hpIncrease,
      currentHp: maxHp + hpIncrease, // Full heal on level up
      attack: attack + attackIncrease,
      defense: defense + defenseIncrease,
      speed: speed + speedIncrease,
    );
  }

  int _calculateExpToNext(int level) {
    return 100 + (level * 50);
  }

  bool canLevelUp() {
    return experience >= experienceToNext;
  }

  double get hpPercentage => currentHp / maxHp;

  Map<String, dynamic> toMap() {
    return {
      'level': level,
      'experience': experience,
      'experienceToNext': experienceToNext,
      'maxHp': maxHp,
      'currentHp': currentHp,
      'attack': attack,
      'defense': defense,
      'speed': speed,
      'gold': gold,
    };
  }

  factory CharacterStats.fromMap(Map<String, dynamic> map) {
    return CharacterStats(
      level: map['level'] ?? 1,
      experience: map['experience'] ?? 0,
      experienceToNext: map['experienceToNext'] ?? 100,
      maxHp: map['maxHp'] ?? 100,
      currentHp: map['currentHp'] ?? 100,
      attack: map['attack'] ?? 10,
      defense: map['defense'] ?? 5,
      speed: map['speed'] ?? 8,
      gold: map['gold'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'CharacterStats(level: $level, hp: $currentHp/$maxHp, attack: $attack, defense: $defense, speed: $speed, gold: $gold)';
  }
}
