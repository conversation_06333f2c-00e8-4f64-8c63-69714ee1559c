import 'employee.dart';
import 'product.dart';

enum Industry { technology, retail, manufacturing, food, healthcare }

class Business {
  final String id;
  final String name;
  final Industry industry;
  final DateTime foundedDate;
  double cash;
  double reputation;
  int level;
  List<Employee> employees;
  List<Product> products;
  double monthlyRent;
  double marketingBudget;
  Map<String, double> monthlyExpenses;
  Map<String, double> monthlyRevenue;
  List<String> unlockedCities;
  
  Business({
    required this.id,
    required this.name,
    required this.industry,
    required this.foundedDate,
    this.cash = 10000.0,
    this.reputation = 50.0,
    this.level = 1,
    List<Employee>? employees,
    List<Product>? products,
    this.monthlyRent = 2000.0,
    this.marketingBudget = 500.0,
    Map<String, double>? monthlyExpenses,
    Map<String, double>? monthlyRevenue,
    List<String>? unlockedCities,
  }) : 
    employees = employees ?? [],
    products = products ?? [],
    monthlyExpenses = monthlyExpenses ?? {},
    monthlyRevenue = monthlyRevenue ?? {},
    unlockedCities = unlockedCities ?? ['New York'];

  // Financial calculations
  double get totalMonthlySalaries {
    return employees.fold(0.0, (sum, employee) => sum + employee.salary);
  }

  double get totalMonthlyExpenses {
    double salaries = totalMonthlySalaries;
    double rent = monthlyRent;
    double marketing = marketingBudget;
    double other = monthlyExpenses.values.fold(0.0, (sum, expense) => sum + expense);
    return salaries + rent + marketing + other;
  }

  double get totalMonthlyRevenue {
    return monthlyRevenue.values.fold(0.0, (sum, revenue) => sum + revenue);
  }

  double get monthlyProfit {
    return totalMonthlyRevenue - totalMonthlyExpenses;
  }

  double get valuation {
    // Simple valuation based on monthly profit, cash, and reputation
    double profitMultiplier = monthlyProfit > 0 ? monthlyProfit * 12 * 5 : 0;
    double cashValue = cash * 0.5;
    double reputationBonus = reputation * 1000;
    return profitMultiplier + cashValue + reputationBonus;
  }

  // Business operations
  void addEmployee(Employee employee) {
    employees.add(employee);
  }

  void removeEmployee(String employeeId) {
    employees.removeWhere((emp) => emp.id == employeeId);
  }

  void addProduct(Product product) {
    products.add(product);
  }

  void removeProduct(String productId) {
    products.removeWhere((prod) => prod.id == productId);
  }

  void addRevenue(String source, double amount) {
    monthlyRevenue[source] = (monthlyRevenue[source] ?? 0) + amount;
    cash += amount;
  }

  void addExpense(String category, double amount) {
    monthlyExpenses[category] = (monthlyExpenses[category] ?? 0) + amount;
    cash -= amount;
  }

  void payMonthlyCosts() {
    // Pay salaries
    for (var employee in employees) {
      cash -= employee.salary;
    }
    
    // Pay rent
    cash -= monthlyRent;
    
    // Pay marketing
    cash -= marketingBudget;
    
    // Pay other expenses
    for (var expense in monthlyExpenses.values) {
      cash -= expense;
    }
  }

  void resetMonthlyStats() {
    monthlyRevenue.clear();
    monthlyExpenses.clear();
  }

  // Industry-specific bonuses
  double get industryBonus {
    switch (industry) {
      case Industry.technology:
        return 1.2; // 20% bonus to revenue
      case Industry.retail:
        return 1.1; // 10% bonus to revenue
      case Industry.manufacturing:
        return 1.15; // 15% bonus to revenue
      case Industry.food:
        return 1.05; // 5% bonus to revenue
      case Industry.healthcare:
        return 1.25; // 25% bonus to revenue
    }
  }

  String get industryName {
    switch (industry) {
      case Industry.technology:
        return 'Technology';
      case Industry.retail:
        return 'Retail';
      case Industry.manufacturing:
        return 'Manufacturing';
      case Industry.food:
        return 'Food & Beverage';
      case Industry.healthcare:
        return 'Healthcare';
    }
  }

  // Level up system
  bool canLevelUp() {
    double requiredValuation = level * 100000.0; // $100k per level
    return valuation >= requiredValuation;
  }

  void levelUp() {
    if (canLevelUp()) {
      level++;
      reputation += 10; // Reputation bonus for leveling up
    }
  }

  // Serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'industry': industry.toString(),
      'foundedDate': foundedDate.millisecondsSinceEpoch,
      'cash': cash,
      'reputation': reputation,
      'level': level,
      'monthlyRent': monthlyRent,
      'marketingBudget': marketingBudget,
      'unlockedCities': unlockedCities.join(','),
    };
  }

  factory Business.fromMap(Map<String, dynamic> map) {
    return Business(
      id: map['id'],
      name: map['name'],
      industry: Industry.values.firstWhere(
        (e) => e.toString() == map['industry'],
        orElse: () => Industry.technology,
      ),
      foundedDate: DateTime.fromMillisecondsSinceEpoch(map['foundedDate']),
      cash: map['cash']?.toDouble() ?? 10000.0,
      reputation: map['reputation']?.toDouble() ?? 50.0,
      level: map['level'] ?? 1,
      monthlyRent: map['monthlyRent']?.toDouble() ?? 2000.0,
      marketingBudget: map['marketingBudget']?.toDouble() ?? 500.0,
      unlockedCities: map['unlockedCities']?.split(',') ?? ['New York'],
    );
  }

  Business copyWith({
    String? id,
    String? name,
    Industry? industry,
    DateTime? foundedDate,
    double? cash,
    double? reputation,
    int? level,
    List<Employee>? employees,
    List<Product>? products,
    double? monthlyRent,
    double? marketingBudget,
    Map<String, double>? monthlyExpenses,
    Map<String, double>? monthlyRevenue,
    List<String>? unlockedCities,
  }) {
    return Business(
      id: id ?? this.id,
      name: name ?? this.name,
      industry: industry ?? this.industry,
      foundedDate: foundedDate ?? this.foundedDate,
      cash: cash ?? this.cash,
      reputation: reputation ?? this.reputation,
      level: level ?? this.level,
      employees: employees ?? this.employees,
      products: products ?? this.products,
      monthlyRent: monthlyRent ?? this.monthlyRent,
      marketingBudget: marketingBudget ?? this.marketingBudget,
      monthlyExpenses: monthlyExpenses ?? this.monthlyExpenses,
      monthlyRevenue: monthlyRevenue ?? this.monthlyRevenue,
      unlockedCities: unlockedCities ?? this.unlockedCities,
    );
  }

  @override
  String toString() {
    return 'Business(name: $name, industry: $industryName, cash: \$${cash.toStringAsFixed(2)}, valuation: \$${valuation.toStringAsFixed(2)})';
  }
}
