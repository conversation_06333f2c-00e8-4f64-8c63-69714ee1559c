import 'package:flutter/material.dart';

class ControlButtons extends StatelessWidget {
  final VoidCallback onMoveLeft;
  final VoidCallback onMoveRight;
  final VoidCallback onShoot;
  final VoidCallback onPause;
  final bool showButtons;

  const ControlButtons({
    Key? key,
    required this.onMoveLeft,
    required this.onMoveRight,
    required this.onShoot,
    required this.onPause,
    this.showButtons = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!showButtons) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Pause button at top right
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildControlButton(
                icon: Icons.pause,
                onPressed: onPause,
                color: Colors.orange,
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Shoot button
          Center(
            child: _buildControlButton(
              icon: Icons.keyboard_arrow_up,
              onPressed: onShoot,
              color: Colors.red,
              size: 60,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Movement buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.keyboard_arrow_left,
                onPressed: onMoveLeft,
                color: Colors.blue,
                size: 60,
              ),
              const SizedBox(width: 100), // Space for shoot button above
              _buildControlButton(
                icon: Icons.keyboard_arrow_right,
                onPressed: onMoveRight,
                color: Colors.blue,
                size: 60,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    double size = 50,
  }) {
    return GestureDetector(
      onTapDown: (_) => onPressed(),
      onTapCancel: () {}, // Handle when finger moves off button
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color.withOpacity(0.7),
          shape: BoxShape.circle,
          border: Border.all(color: color, width: 2),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: size * 0.6,
        ),
      ),
    );
  }
}
