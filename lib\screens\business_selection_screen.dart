import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/business_provider.dart';
import '../models/business.dart';
import 'dashboard_screen.dart';

class BusinessSelectionScreen extends StatelessWidget {
  const BusinessSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Load Business'),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0D1421),
              Color(0xFF1E2A3A),
            ],
          ),
        ),
        child: Consumer<BusinessProvider>(
          builder: (context, businessProvider, child) {
            if (businessProvider.savedBusinesses.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.business_center,
                      size: 80,
                      color: Colors.white54,
                    ),
                    Sized<PERSON>ox(height: 16),
                    Text(
                      'No Saved Businesses',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Create your first business to get started!',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: businessProvider.savedBusinesses.length,
              itemBuilder: (context, index) {
                final business = businessProvider.savedBusinesses[index];
                return _buildBusinessCard(context, business, businessProvider);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildBusinessCard(BuildContext context, Business business, BusinessProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 8,
      child: InkWell(
        onTap: () => _loadBusiness(context, business.id, provider),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          _getIndustryColor(business.industry),
                          _getIndustryColor(business.industry).withOpacity(0.7),
                        ],
                      ),
                    ),
                    child: Icon(
                      _getIndustryIcon(business.industry),
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          business.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          business.industryName,
                          style: TextStyle(
                            fontSize: 14,
                            color: _getIndustryColor(business.industry),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        _showDeleteConfirmation(context, business, provider);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildStatItem(
                    'Cash',
                    '\$${_formatNumber(business.cash)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    'Level',
                    '${business.level}',
                    Icons.star,
                    Colors.amber,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    'Valuation',
                    '\$${_formatNumber(business.valuation)}',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.access_time, color: Colors.white70, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Founded ${_formatDate(business.foundedDate)}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${business.employees.length} employees',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getIndustryColor(Industry industry) {
    switch (industry) {
      case Industry.technology:
        return const Color(0xFF2196F3);
      case Industry.retail:
        return const Color(0xFFFF9800);
      case Industry.manufacturing:
        return const Color(0xFF795548);
      case Industry.food:
        return const Color(0xFFE91E63);
      case Industry.healthcare:
        return const Color(0xFF4CAF50);
    }
  }

  IconData _getIndustryIcon(Industry industry) {
    switch (industry) {
      case Industry.technology:
        return Icons.computer;
      case Industry.retail:
        return Icons.shopping_bag;
      case Industry.manufacturing:
        return Icons.precision_manufacturing;
      case Industry.food:
        return Icons.restaurant;
      case Industry.healthcare:
        return Icons.local_hospital;
    }
  }

  String _formatNumber(double number) {
    if (number >= **********) {
      return '${(number / **********).toStringAsFixed(1)}B';
    } else if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toStringAsFixed(0);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _loadBusiness(BuildContext context, String businessId, BusinessProvider provider) async {
    await provider.loadBusiness(businessId);
    
    if (context.mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const DashboardScreen()),
        (route) => false,
      );
    }
  }

  void _showDeleteConfirmation(BuildContext context, Business business, BusinessProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Delete Business'),
        content: Text('Are you sure you want to delete "${business.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.deleteBusiness(business.id);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
