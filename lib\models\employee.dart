enum EmployeeRole { 
  sales, 
  engineer, 
  manager, 
  marketing, 
  finance, 
  hr 
}

enum SkillLevel { 
  junior, 
  mid, 
  senior, 
  expert 
}

class Employee {
  final String id;
  final String name;
  final EmployeeRole role;
  final SkillLevel skillLevel;
  final double salary;
  final DateTime hiredDate;
  double productivity;
  double satisfaction;
  int experience;
  
  Employee({
    required this.id,
    required this.name,
    required this.role,
    required this.skillLevel,
    required this.salary,
    required this.hiredDate,
    this.productivity = 1.0,
    this.satisfaction = 75.0,
    this.experience = 0,
  });

  // Productivity calculations
  double get effectiveProductivity {
    double baseProductivity = productivity;
    double skillMultiplier = _getSkillMultiplier();
    double satisfactionMultiplier = satisfaction / 100.0;
    return baseProductivity * skillMultiplier * satisfactionMultiplier;
  }

  double _getSkillMultiplier() {
    switch (skillLevel) {
      case SkillLevel.junior:
        return 0.7;
      case SkillLevel.mid:
        return 1.0;
      case SkillLevel.senior:
        return 1.3;
      case SkillLevel.expert:
        return 1.6;
    }
  }

  // Role-specific contributions
  double get salesContribution {
    if (role == EmployeeRole.sales) {
      return effectiveProductivity * 1000; // $1000 base sales per productivity point
    }
    return 0;
  }

  double get engineeringContribution {
    if (role == EmployeeRole.engineer) {
      return effectiveProductivity * 0.1; // 0.1 product quality improvement per productivity point
    }
    return 0;
  }

  double get managementContribution {
    if (role == EmployeeRole.manager) {
      return effectiveProductivity * 0.05; // 5% team productivity boost per productivity point
    }
    return 0;
  }

  double get marketingContribution {
    if (role == EmployeeRole.marketing) {
      return effectiveProductivity * 50; // $50 marketing value per productivity point
    }
    return 0;
  }

  double get financeContribution {
    if (role == EmployeeRole.finance) {
      return effectiveProductivity * 0.02; // 2% cost reduction per productivity point
    }
    return 0;
  }

  double get hrContribution {
    if (role == EmployeeRole.hr) {
      return effectiveProductivity * 2; // 2 satisfaction points per productivity point
    }
    return 0;
  }

  // Employee development
  void gainExperience(int points) {
    experience += points;
    
    // Increase productivity based on experience
    if (experience % 100 == 0) {
      productivity = (productivity + 0.1).clamp(0.5, 2.0);
    }
  }

  void updateSatisfaction(double change) {
    satisfaction = (satisfaction + change).clamp(0.0, 100.0);
  }

  bool canPromote() {
    return experience >= _getPromotionRequirement() && skillLevel != SkillLevel.expert;
  }

  int _getPromotionRequirement() {
    switch (skillLevel) {
      case SkillLevel.junior:
        return 200;
      case SkillLevel.mid:
        return 500;
      case SkillLevel.senior:
        return 1000;
      case SkillLevel.expert:
        return 9999; // Cannot promote further
    }
  }

  Employee promote() {
    if (!canPromote()) return this;
    
    SkillLevel newSkillLevel;
    double newSalary = salary * 1.3; // 30% salary increase
    
    switch (skillLevel) {
      case SkillLevel.junior:
        newSkillLevel = SkillLevel.mid;
        break;
      case SkillLevel.mid:
        newSkillLevel = SkillLevel.senior;
        break;
      case SkillLevel.senior:
        newSkillLevel = SkillLevel.expert;
        break;
      case SkillLevel.expert:
        newSkillLevel = SkillLevel.expert;
        break;
    }
    
    return Employee(
      id: id,
      name: name,
      role: role,
      skillLevel: newSkillLevel,
      salary: newSalary,
      hiredDate: hiredDate,
      productivity: productivity,
      satisfaction: (satisfaction + 20).clamp(0, 100), // Promotion boost
      experience: experience,
    );
  }

  // Getters for display
  String get roleName {
    switch (role) {
      case EmployeeRole.sales:
        return 'Sales';
      case EmployeeRole.engineer:
        return 'Engineer';
      case EmployeeRole.manager:
        return 'Manager';
      case EmployeeRole.marketing:
        return 'Marketing';
      case EmployeeRole.finance:
        return 'Finance';
      case EmployeeRole.hr:
        return 'HR';
    }
  }

  String get skillLevelName {
    switch (skillLevel) {
      case SkillLevel.junior:
        return 'Junior';
      case SkillLevel.mid:
        return 'Mid-level';
      case SkillLevel.senior:
        return 'Senior';
      case SkillLevel.expert:
        return 'Expert';
    }
  }

  String get fullTitle => '$skillLevelName $roleName';

  // Serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'role': role.toString(),
      'skillLevel': skillLevel.toString(),
      'salary': salary,
      'hiredDate': hiredDate.millisecondsSinceEpoch,
      'productivity': productivity,
      'satisfaction': satisfaction,
      'experience': experience,
    };
  }

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'],
      name: map['name'],
      role: EmployeeRole.values.firstWhere(
        (e) => e.toString() == map['role'],
        orElse: () => EmployeeRole.sales,
      ),
      skillLevel: SkillLevel.values.firstWhere(
        (e) => e.toString() == map['skillLevel'],
        orElse: () => SkillLevel.junior,
      ),
      salary: map['salary']?.toDouble() ?? 3000.0,
      hiredDate: DateTime.fromMillisecondsSinceEpoch(map['hiredDate']),
      productivity: map['productivity']?.toDouble() ?? 1.0,
      satisfaction: map['satisfaction']?.toDouble() ?? 75.0,
      experience: map['experience'] ?? 0,
    );
  }

  Employee copyWith({
    String? id,
    String? name,
    EmployeeRole? role,
    SkillLevel? skillLevel,
    double? salary,
    DateTime? hiredDate,
    double? productivity,
    double? satisfaction,
    int? experience,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      skillLevel: skillLevel ?? this.skillLevel,
      salary: salary ?? this.salary,
      hiredDate: hiredDate ?? this.hiredDate,
      productivity: productivity ?? this.productivity,
      satisfaction: satisfaction ?? this.satisfaction,
      experience: experience ?? this.experience,
    );
  }

  @override
  String toString() {
    return 'Employee(name: $name, title: $fullTitle, salary: \$${salary.toStringAsFixed(0)}, satisfaction: ${satisfaction.toStringAsFixed(1)}%)';
  }
}

// Employee templates for hiring
class EmployeeTemplates {
  static List<Employee> getAvailableEmployees() {
    final names = [
      'Alex Johnson', 'Sarah Chen', 'Mike Rodriguez', 'Emily Davis',
      'David Kim', 'Lisa Wang', 'John Smith', 'Maria Garcia',
      'Chris Brown', 'Anna Wilson', 'Tom Anderson', 'Jessica Lee'
    ];
    
    final employees = <Employee>[];
    
    for (int i = 0; i < names.length; i++) {
      final role = EmployeeRole.values[i % EmployeeRole.values.length];
      final skillLevel = _getRandomSkillLevel();
      final salary = _calculateSalary(role, skillLevel);
      
      employees.add(Employee(
        id: 'emp_${DateTime.now().millisecondsSinceEpoch}_$i',
        name: names[i],
        role: role,
        skillLevel: skillLevel,
        salary: salary,
        hiredDate: DateTime.now(),
      ));
    }
    
    return employees;
  }
  
  static SkillLevel _getRandomSkillLevel() {
    // Weighted random: more juniors, fewer experts
    final random = DateTime.now().millisecond % 100;
    if (random < 50) return SkillLevel.junior;
    if (random < 80) return SkillLevel.mid;
    if (random < 95) return SkillLevel.senior;
    return SkillLevel.expert;
  }
  
  static double _calculateSalary(EmployeeRole role, SkillLevel skillLevel) {
    double baseSalary;
    
    switch (role) {
      case EmployeeRole.sales:
        baseSalary = 3000;
        break;
      case EmployeeRole.engineer:
        baseSalary = 4000;
        break;
      case EmployeeRole.manager:
        baseSalary = 5000;
        break;
      case EmployeeRole.marketing:
        baseSalary = 3500;
        break;
      case EmployeeRole.finance:
        baseSalary = 4500;
        break;
      case EmployeeRole.hr:
        baseSalary = 3200;
        break;
    }
    
    switch (skillLevel) {
      case SkillLevel.junior:
        return baseSalary * 0.7;
      case SkillLevel.mid:
        return baseSalary;
      case SkillLevel.senior:
        return baseSalary * 1.5;
      case SkillLevel.expert:
        return baseSalary * 2.2;
    }
  }
}
