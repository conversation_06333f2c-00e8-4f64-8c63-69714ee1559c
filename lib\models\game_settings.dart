class GameSettings {
  final int? id;
  final bool soundEnabled;
  final bool musicEnabled;
  final double soundVolume;
  final double musicVolume;
  final String controlType; // 'touch', 'buttons'

  GameSettings({
    this.id,
    this.soundEnabled = true,
    this.musicEnabled = true,
    this.soundVolume = 0.7,
    this.musicVolume = 0.5,
    this.controlType = 'touch',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'soundEnabled': soundEnabled ? 1 : 0,
      'musicEnabled': musicEnabled ? 1 : 0,
      'soundVolume': soundVolume,
      'musicVolume': musicVolume,
      'controlType': controlType,
    };
  }

  factory GameSettings.fromMap(Map<String, dynamic> map) {
    return GameSettings(
      id: map['id'],
      soundEnabled: map['soundEnabled'] == 1,
      musicEnabled: map['musicEnabled'] == 1,
      soundVolume: map['soundVolume'],
      musicVolume: map['musicVolume'],
      controlType: map['controlType'],
    );
  }

  GameSettings copyWith({
    int? id,
    bool? soundEnabled,
    bool? musicEnabled,
    double? soundVolume,
    double? musicVolume,
    String? controlType,
  }) {
    return GameSettings(
      id: id ?? this.id,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      soundVolume: soundVolume ?? this.soundVolume,
      musicVolume: musicVolume ?? this.musicVolume,
      controlType: controlType ?? this.controlType,
    );
  }

  @override
  String toString() {
    return 'GameSettings{id: $id, soundEnabled: $soundEnabled, musicEnabled: $musicEnabled, soundVolume: $soundVolume, musicVolume: $musicVolume, controlType: $controlType}';
  }
}
