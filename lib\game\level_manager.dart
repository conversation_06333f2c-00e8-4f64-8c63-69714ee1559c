import '../models/alien.dart';
import 'dart:math';

class LevelManager {
  static List<Alien> generateAliensForLevel(int level, double screenWidth, double screenHeight) {
    List<Alien> aliens = [];
    
    // Calculate grid dimensions
    int rows = _getRowsForLevel(level);
    int cols = _getColsForLevel(level);
    
    double alienSpacingX = screenWidth * 0.8 / cols;
    double alienSpacingY = 40.0;
    double startX = screenWidth * 0.1;
    double startY = screenHeight * 0.1;
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        AlienType type = _getAlienTypeForPosition(row, level);
        
        double x = startX + col * alienSpacingX;
        double y = startY + row * alienSpacingY;
        
        aliens.add(Alien(
          x: x,
          y: y,
          type: type,
        ));
      }
    }
    
    return aliens;
  }
  
  static int _getRowsForLevel(int level) {
    // Start with 3 rows, add more for higher levels
    return min(3 + (level - 1) ~/ 2, 6);
  }
  
  static int _getColsForLevel(int level) {
    // Start with 8 columns, add more for higher levels
    return min(8 + (level - 1) ~/ 3, 12);
  }
  
  static AlienType _getAlienTypeForPosition(int row, int level) {
    // Top rows are stronger aliens
    if (row == 0 && level >= 3) {
      return AlienType.strong;
    } else if (row <= 1 && level >= 2) {
      return AlienType.fast;
    } else {
      return AlienType.basic;
    }
  }
  
  static double getAlienSpeedMultiplier(int level) {
    return 1.0 + (level - 1) * 0.2;
  }
  
  static double getAlienShootFrequencyMultiplier(int level) {
    return 1.0 + (level - 1) * 0.3;
  }
  
  static int getBonusPointsForLevel(int level) {
    return level * 100;
  }
  
  static String getLevelDescription(int level) {
    switch (level) {
      case 1:
        return "Welcome to Space Invaders!";
      case 2:
        return "Fast aliens incoming!";
      case 3:
        return "Strong aliens detected!";
      case 4:
        return "Mixed formation!";
      case 5:
        return "Heavy resistance!";
      default:
        return "Level $level - Extreme difficulty!";
    }
  }
  
  static bool shouldShowLevelIntro(int level) {
    return level <= 5 || level % 5 == 0;
  }
}
