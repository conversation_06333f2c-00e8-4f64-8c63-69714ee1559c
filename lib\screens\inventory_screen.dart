import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../models/item.dart';
import '../models/inventory.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inventory'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(icon: Icon(Icons.all_inclusive), text: 'All'),
            Tab(icon: Icon(Icons.local_fire_department), text: 'Weapons'),
            Tab(icon: Icon(Icons.shield), text: 'Armor'),
            Tab(icon: Icon(Icons.local_pharmacy), text: 'Items'),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: Consumer<GameProvider>(
          builder: (context, gameProvider, child) {
            final inventory = gameProvider.inventory;

            return Column(
              children: [
                _buildInventoryHeader(inventory),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildItemGrid(inventory.slots, gameProvider),
                      _buildItemGrid(inventory.weapons, gameProvider),
                      _buildItemGrid(inventory.armor, gameProvider),
                      _buildItemGrid(inventory.consumables, gameProvider),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildInventoryHeader(Inventory inventory) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildHeaderStat(
              'Slots',
              '${inventory.slots.length}/${inventory.maxSlots}',
              Icons.inventory,
              Colors.blue,
            ),
            _buildHeaderStat(
              'Value',
              '${inventory.totalValue}g',
              Icons.monetization_on,
              Colors.yellow,
            ),
            _buildHeaderStat(
              'Items',
              '${inventory.slots.fold(0, (sum, slot) => sum + slot.quantity)}',
              Icons.category,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildItemGrid(List<InventorySlot> slots, GameProvider gameProvider) {
    if (slots.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.white54,
            ),
            SizedBox(height: 16),
            Text(
              'No items found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: slots.length,
      itemBuilder: (context, index) {
        return _buildItemCard(slots[index], gameProvider);
      },
    );
  }

  Widget _buildItemCard(InventorySlot slot, GameProvider gameProvider) {
    final item = slot.item;
    final rarityColor = _getRarityColor(item.rarity);

    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _showItemDialog(item, slot.quantity, gameProvider),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: rarityColor, width: 2),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Item Icon and Quantity
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: rarityColor.withOpacity(0.2),
                        border: Border.all(color: rarityColor),
                      ),
                      child: Icon(
                        _getItemIcon(item.type),
                        color: rarityColor,
                        size: 20,
                      ),
                    ),
                    if (slot.quantity > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '${slot.quantity}',
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 8),

                // Item Name
                Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // Item Type
                Text(
                  item.type.name.toUpperCase(),
                  style: TextStyle(
                    color: rarityColor,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const Spacer(),

                // Item Stats or Effect
                if (item.consumable && item.effect != null)
                  Text(
                    _getEffectText(item),
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                else if (!item.consumable)
                  Text(
                    _getItemStatsText(item),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 11,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                const SizedBox(height: 4),

                // Item Value
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          color: Colors.yellow,
                          size: 12,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${item.value}',
                          style: const TextStyle(
                            color: Colors.yellow,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: rarityColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getRarityColor(ItemRarity rarity) {
    switch (rarity) {
      case ItemRarity.common:
        return Colors.grey;
      case ItemRarity.uncommon:
        return Colors.green;
      case ItemRarity.rare:
        return Colors.blue;
      case ItemRarity.epic:
        return Colors.purple;
      case ItemRarity.legendary:
        return Colors.orange;
    }
  }

  IconData _getItemIcon(ItemType type) {
    switch (type) {
      case ItemType.weapon:
        return Icons.local_fire_department;
      case ItemType.armor:
        return Icons.shield;
      case ItemType.consumable:
        return Icons.local_pharmacy;
      case ItemType.accessory:
        return Icons.diamond;
    }
  }

  String _getItemStatsText(Item item) {
    final stats = <String>[];
    if (item.attackBonus > 0) stats.add('+${item.attackBonus} ATK');
    if (item.defenseBonus > 0) stats.add('+${item.defenseBonus} DEF');
    if (item.speedBonus > 0) stats.add('+${item.speedBonus} SPD');
    if (item.hpBonus > 0) stats.add('+${item.hpBonus} HP');
    return stats.join(', ');
  }

  String _getEffectText(Item item) {
    switch (item.effect) {
      case 'heal':
        return 'Heals ${item.effectValue} HP';
      case 'attack_buff':
        return '+${item.effectValue} ATK (battle)';
      default:
        return item.effect ?? '';
    }
  }

  void _showItemDialog(Item item, int quantity, GameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Row(
          children: [
            Icon(
              _getItemIcon(item.type),
              color: _getRarityColor(item.rarity),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                item.name,
                style: TextStyle(
                  color: _getRarityColor(item.rarity),
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.description,
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 12),
            if (!item.consumable && _getItemStatsText(item).isNotEmpty) ...[
              const Text(
                'Stats:',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _getItemStatsText(item),
                style: const TextStyle(color: Colors.green),
              ),
              const SizedBox(height: 8),
            ],
            if (item.consumable && item.effect != null) ...[
              const Text(
                'Effect:',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _getEffectText(item),
                style: const TextStyle(color: Colors.green),
              ),
              const SizedBox(height: 8),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Quantity: $quantity',
                  style: const TextStyle(color: Colors.white70),
                ),
                Text(
                  'Value: ${item.value} gold',
                  style: const TextStyle(color: Colors.yellow),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (item.consumable)
            TextButton(
              onPressed: () {
                gameProvider.useConsumableItem(item);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Used ${item.name}'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Use'),
            )
          else
            TextButton(
              onPressed: () {
                gameProvider.equipItem(item);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Equipped ${item.name}'),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              child: const Text('Equip'),
            ),
        ],
      ),
    );
  }
}
