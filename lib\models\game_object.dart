import 'dart:ui';

class GameSize {
  final double width;
  final double height;

  GameSize(this.width, this.height);
}

abstract class GameObject {
  double x;
  double y;
  double width;
  double height;
  bool isActive;

  GameObject({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.isActive = true,
  });

  // Get the bounding rectangle for collision detection
  Rect get bounds => Rect.fromLTWH(x, y, width, height);

  // Check collision with another game object
  bool collidesWith(GameObject other) {
    return bounds.overlaps(other.bounds);
  }

  // Update the object's state
  void update(double deltaTime);

  // Render the object
  void render(Canvas canvas, Size size);

  // Check if object is within screen bounds
  bool isOnScreen(GameSize screenSize) {
    return x + width >= 0 &&
           x <= screenSize.width &&
           y + height >= 0 &&
           y <= screenSize.height;
  }
}
