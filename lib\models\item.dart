enum ItemType { weapon, armor, consumable, accessory }

enum ItemRarity { common, uncommon, rare, epic, legendary }

class Item {
  final String id;
  final String name;
  final String description;
  final ItemType type;
  final ItemRarity rarity;
  final int value; // Gold value
  final Map<String, int> stats; // stat bonuses (attack, defense, speed, hp)
  final bool consumable;
  final String? effect; // For consumables (heal, buff, etc.)
  final int? effectValue; // Amount of effect

  Item({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    this.rarity = ItemRarity.common,
    required this.value,
    this.stats = const {},
    this.consumable = false,
    this.effect,
    this.effectValue,
  });

  // Stat getters
  int get attackBonus => stats['attack'] ?? 0;
  int get defenseBonus => stats['defense'] ?? 0;
  int get speedBonus => stats['speed'] ?? 0;
  int get hpBonus => stats['hp'] ?? 0;

  // Rarity color for UI
  String get rarityColor {
    switch (rarity) {
      case ItemRarity.common:
        return '#FFFFFF';
      case ItemRarity.uncommon:
        return '#00FF00';
      case ItemRarity.rare:
        return '#0080FF';
      case ItemRarity.epic:
        return '#8000FF';
      case ItemRarity.legendary:
        return '#FF8000';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString(),
      'rarity': rarity.toString(),
      'value': value,
      'stats': stats.toString(), // Convert map to string for storage
      'consumable': consumable ? 1 : 0,
      'effect': effect,
      'effectValue': effectValue,
    };
  }

  factory Item.fromMap(Map<String, dynamic> map) {
    // Parse stats string back to map
    Map<String, int> parsedStats = {};
    if (map['stats'] != null && map['stats'].toString().isNotEmpty) {
      // Simple parsing - in production, use JSON encoding
      final statsStr = map['stats'].toString();
      if (statsStr.contains('attack')) {
        final match = RegExp(r'attack:\s*(\d+)').firstMatch(statsStr);
        if (match != null) parsedStats['attack'] = int.parse(match.group(1)!);
      }
      if (statsStr.contains('defense')) {
        final match = RegExp(r'defense:\s*(\d+)').firstMatch(statsStr);
        if (match != null) parsedStats['defense'] = int.parse(match.group(1)!);
      }
      if (statsStr.contains('speed')) {
        final match = RegExp(r'speed:\s*(\d+)').firstMatch(statsStr);
        if (match != null) parsedStats['speed'] = int.parse(match.group(1)!);
      }
      if (statsStr.contains('hp')) {
        final match = RegExp(r'hp:\s*(\d+)').firstMatch(statsStr);
        if (match != null) parsedStats['hp'] = int.parse(match.group(1)!);
      }
    }

    return Item(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ItemType.values.firstWhere((e) => e.toString() == map['type']),
      rarity: ItemRarity.values.firstWhere((e) => e.toString() == map['rarity']),
      value: map['value'],
      stats: parsedStats,
      consumable: map['consumable'] == 1,
      effect: map['effect'],
      effectValue: map['effectValue'],
    );
  }

  @override
  String toString() {
    return 'Item(name: $name, type: $type, rarity: $rarity, value: $value)';
  }
}

// Predefined items
class ItemDatabase {
  static final List<Item> weapons = [
    Item(
      id: 'rusty_sword',
      name: 'Rusty Sword',
      description: 'An old, worn sword. Better than nothing.',
      type: ItemType.weapon,
      rarity: ItemRarity.common,
      value: 10,
      stats: {'attack': 5},
    ),
    Item(
      id: 'iron_sword',
      name: 'Iron Sword',
      description: 'A sturdy iron blade.',
      type: ItemType.weapon,
      rarity: ItemRarity.uncommon,
      value: 50,
      stats: {'attack': 12},
    ),
    Item(
      id: 'steel_sword',
      name: 'Steel Sword',
      description: 'A well-crafted steel weapon.',
      type: ItemType.weapon,
      rarity: ItemRarity.rare,
      value: 150,
      stats: {'attack': 20},
    ),
    Item(
      id: 'dragon_blade',
      name: 'Dragon Blade',
      description: 'Forged from dragon scales, this blade burns with power.',
      type: ItemType.weapon,
      rarity: ItemRarity.legendary,
      value: 1000,
      stats: {'attack': 35, 'speed': 5},
    ),
  ];

  static final List<Item> armor = [
    Item(
      id: 'leather_armor',
      name: 'Leather Armor',
      description: 'Basic protection made from animal hide.',
      type: ItemType.armor,
      rarity: ItemRarity.common,
      value: 15,
      stats: {'defense': 3},
    ),
    Item(
      id: 'chain_mail',
      name: 'Chain Mail',
      description: 'Interlocked metal rings provide good protection.',
      type: ItemType.armor,
      rarity: ItemRarity.uncommon,
      value: 75,
      stats: {'defense': 8},
    ),
    Item(
      id: 'plate_armor',
      name: 'Plate Armor',
      description: 'Heavy metal plates offer excellent defense.',
      type: ItemType.armor,
      rarity: ItemRarity.rare,
      value: 200,
      stats: {'defense': 15, 'hp': 20},
    ),
    Item(
      id: 'dragon_scale_armor',
      name: 'Dragon Scale Armor',
      description: 'Armor crafted from ancient dragon scales.',
      type: ItemType.armor,
      rarity: ItemRarity.legendary,
      value: 1500,
      stats: {'defense': 25, 'hp': 50, 'speed': -2},
    ),
  ];

  static final List<Item> consumables = [
    Item(
      id: 'health_potion',
      name: 'Health Potion',
      description: 'Restores 50 HP.',
      type: ItemType.consumable,
      rarity: ItemRarity.common,
      value: 25,
      consumable: true,
      effect: 'heal',
      effectValue: 50,
    ),
    Item(
      id: 'greater_health_potion',
      name: 'Greater Health Potion',
      description: 'Restores 100 HP.',
      type: ItemType.consumable,
      rarity: ItemRarity.uncommon,
      value: 60,
      consumable: true,
      effect: 'heal',
      effectValue: 100,
    ),
    Item(
      id: 'strength_potion',
      name: 'Strength Potion',
      description: 'Temporarily increases attack by 10 for one battle.',
      type: ItemType.consumable,
      rarity: ItemRarity.rare,
      value: 100,
      consumable: true,
      effect: 'attack_buff',
      effectValue: 10,
    ),
  ];

  static final List<Item> accessories = [
    Item(
      id: 'speed_ring',
      name: 'Ring of Speed',
      description: 'A magical ring that enhances agility.',
      type: ItemType.accessory,
      rarity: ItemRarity.uncommon,
      value: 80,
      stats: {'speed': 5},
    ),
    Item(
      id: 'power_amulet',
      name: 'Amulet of Power',
      description: 'An ancient amulet that boosts strength.',
      type: ItemType.accessory,
      rarity: ItemRarity.rare,
      value: 120,
      stats: {'attack': 8, 'hp': 15},
    ),
  ];

  static List<Item> get allItems => [...weapons, ...armor, ...consumables, ...accessories];
  
  static Item? getItemById(String id) {
    try {
      return allItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }
}
