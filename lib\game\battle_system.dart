import 'dart:math';
import '../models/character.dart';
import '../models/enemy.dart';
import '../models/item.dart';

enum BattlePhase { 
  start, 
  playerTurn, 
  enemyTurn, 
  victory, 
  defeat, 
  fled 
}

enum BattleResult { 
  victory, 
  defeat, 
  fled 
}

class BattleState {
  final Character player;
  final Enemy enemy;
  final BattlePhase phase;
  final List<String> battleLog;
  final bool playerDefending;
  final bool enemyDefending;
  final int turnCount;

  BattleState({
    required this.player,
    required this.enemy,
    this.phase = BattlePhase.start,
    this.battleLog = const [],
    this.playerDefending = false,
    this.enemyDefending = false,
    this.turnCount = 0,
  });

  BattleState copyWith({
    Character? player,
    Enemy? enemy,
    BattlePhase? phase,
    List<String>? battleLog,
    bool? playerDefending,
    bool? enemyDefending,
    int? turnCount,
  }) {
    return BattleState(
      player: player ?? this.player,
      enemy: enemy ?? this.enemy,
      phase: phase ?? this.phase,
      battleLog: battleLog ?? this.battleLog,
      playerDefending: playerDefending ?? this.playerDefending,
      enemyDefending: enemyDefending ?? this.enemyDefending,
      turnCount: turnCount ?? this.turnCount,
    );
  }

  BattleState addLogEntry(String entry) {
    final newLog = List<String>.from(battleLog);
    newLog.add(entry);
    return copyWith(battleLog: newLog);
  }
}

class BattleSystem {
  static BattleState initializeBattle(Character player, Enemy enemy) {
    return BattleState(
      player: player,
      enemy: enemy,
      phase: BattlePhase.start,
      battleLog: ['Battle begins! ${player.name} vs ${enemy.name}'],
    );
  }

  static BattleState startTurn(BattleState state) {
    // Determine turn order based on speed
    final playerSpeed = state.player.totalStats.speed;
    final enemySpeed = state.enemy.stats.speed;
    
    if (playerSpeed >= enemySpeed) {
      return state.copyWith(
        phase: BattlePhase.playerTurn,
        turnCount: state.turnCount + 1,
      );
    } else {
      return state.copyWith(
        phase: BattlePhase.enemyTurn,
        turnCount: state.turnCount + 1,
      );
    }
  }

  static BattleState playerAttack(BattleState state) {
    final player = state.player;
    final enemy = state.enemy;
    
    // Calculate damage
    final baseDamage = player.totalStats.attack;
    final random = Random();
    final variance = (baseDamage * 0.15).round(); // ±15% variance
    var damage = baseDamage + random.nextInt(variance * 2) - variance;
    
    // Apply enemy defense
    damage = (damage - enemy.stats.defense).clamp(1, damage);
    
    // Apply defending bonus
    if (state.enemyDefending) {
      damage = (damage * 0.5).round();
    }
    
    // Critical hit chance (10%)
    bool isCritical = random.nextDouble() < 0.1;
    if (isCritical) {
      damage = (damage * 1.5).round();
    }
    
    final newEnemy = enemy.takeDamage(damage);
    String logEntry = '${player.name} attacks for $damage damage!';
    if (isCritical) {
      logEntry += ' Critical hit!';
    }
    
    var newState = state.copyWith(
      enemy: newEnemy,
      playerDefending: false,
    ).addLogEntry(logEntry);
    
    // Check if enemy is defeated
    if (!newEnemy.isAlive) {
      newState = newState.copyWith(phase: BattlePhase.victory);
      newState = newState.addLogEntry('${enemy.name} is defeated!');
    } else {
      newState = newState.copyWith(phase: BattlePhase.enemyTurn);
    }
    
    return newState;
  }

  static BattleState playerDefend(BattleState state) {
    var newState = state.copyWith(
      playerDefending: true,
      phase: BattlePhase.enemyTurn,
    ).addLogEntry('${state.player.name} takes a defensive stance.');
    
    return newState;
  }

  static BattleState playerUseItem(BattleState state, Item item) {
    var player = state.player;
    String logEntry = '';
    
    switch (item.effect) {
      case 'heal':
        final healAmount = item.effectValue ?? 0;
        player = player.heal(healAmount);
        logEntry = '${player.name} uses ${item.name} and recovers $healAmount HP!';
        break;
        
      case 'attack_buff':
        final buffAmount = item.effectValue ?? 0;
        player = player.addTemporaryBuff('attack', buffAmount);
        logEntry = '${player.name} uses ${item.name} and gains +$buffAmount attack!';
        break;
        
      default:
        logEntry = '${player.name} uses ${item.name} but nothing happens.';
    }
    
    var newState = state.copyWith(
      player: player,
      phase: BattlePhase.enemyTurn,
    ).addLogEntry(logEntry);
    
    return newState;
  }

  static BattleState playerFlee(BattleState state) {
    final random = Random();
    final playerSpeed = state.player.totalStats.speed;
    final enemySpeed = state.enemy.stats.speed;
    
    // Flee chance based on speed difference
    double fleeChance = 0.5 + ((playerSpeed - enemySpeed) * 0.05);
    fleeChance = fleeChance.clamp(0.1, 0.9);
    
    if (random.nextDouble() < fleeChance) {
      return state.copyWith(
        phase: BattlePhase.fled,
      ).addLogEntry('${state.player.name} successfully flees from battle!');
    } else {
      return state.copyWith(
        phase: BattlePhase.enemyTurn,
      ).addLogEntry('${state.player.name} tries to flee but fails!');
    }
  }

  static BattleState enemyTurn(BattleState state) {
    final enemy = state.enemy;
    final player = state.player;
    
    // Enemy AI chooses action
    final action = enemy.chooseAction(player.totalStats);
    
    switch (action) {
      case BattleAction.attack:
        return _enemyAttack(state);
      case BattleAction.defend:
        return _enemyDefend(state);
      case BattleAction.ability:
        return _enemyUseAbility(state);
      default:
        return _enemyAttack(state); // Fallback to attack
    }
  }

  static BattleState _enemyAttack(BattleState state) {
    final enemy = state.enemy;
    final player = state.player;
    
    // Calculate damage
    final baseDamage = enemy.stats.attack;
    final random = Random();
    final variance = (baseDamage * 0.15).round();
    var damage = baseDamage + random.nextInt(variance * 2) - variance;
    
    // Apply player defense
    damage = (damage - player.totalStats.defense).clamp(1, damage);
    
    // Apply defending bonus
    if (state.playerDefending) {
      damage = (damage * 0.5).round();
    }
    
    final newPlayer = player.takeDamage(damage);
    String logEntry = '${enemy.name} attacks for $damage damage!';
    
    var newState = state.copyWith(
      player: newPlayer,
      enemyDefending: false,
    ).addLogEntry(logEntry);
    
    // Check if player is defeated
    if (!newPlayer.isAlive) {
      newState = newState.copyWith(phase: BattlePhase.defeat);
      newState = newState.addLogEntry('${player.name} is defeated!');
    } else {
      newState = newState.copyWith(phase: BattlePhase.playerTurn);
    }
    
    return newState;
  }

  static BattleState _enemyDefend(BattleState state) {
    return state.copyWith(
      enemyDefending: true,
      phase: BattlePhase.playerTurn,
    ).addLogEntry('${state.enemy.name} takes a defensive stance.');
  }

  static BattleState _enemyUseAbility(BattleState state) {
    final enemy = state.enemy;
    final random = Random();
    
    if (enemy.abilities.isEmpty) {
      return _enemyAttack(state); // Fallback to attack
    }
    
    final ability = enemy.abilities[random.nextInt(enemy.abilities.length)];
    String logEntry = '';
    var newPlayer = state.player;
    
    switch (ability) {
      case 'rage':
        // Orc rage - increased damage next turn
        logEntry = '${enemy.name} enters a rage! Attack increased!';
        break;
        
      case 'bone_spear':
        // Skeleton bone spear - magic damage
        final damage = (enemy.stats.attack * 1.2).round();
        final actualDamage = (damage - newPlayer.totalStats.defense).clamp(1, damage);
        newPlayer = newPlayer.takeDamage(actualDamage);
        logEntry = '${enemy.name} casts Bone Spear for $actualDamage damage!';
        break;
        
      case 'dark_magic':
        // Skeleton dark magic - ignores defense
        final damage = enemy.stats.attack;
        newPlayer = newPlayer.takeDamage(damage);
        logEntry = '${enemy.name} casts Dark Magic for $damage damage!';
        break;
        
      case 'fire_breath':
        // Dragon fire breath - high damage
        final damage = (enemy.stats.attack * 1.5).round();
        final actualDamage = (damage - newPlayer.totalStats.defense).clamp(damage ~/ 2, damage);
        newPlayer = newPlayer.takeDamage(actualDamage);
        logEntry = '${enemy.name} breathes fire for $actualDamage damage!';
        break;
        
      case 'wing_attack':
        // Dragon wing attack - medium damage, chance to stun
        final damage = enemy.stats.attack;
        final actualDamage = (damage - newPlayer.totalStats.defense).clamp(1, damage);
        newPlayer = newPlayer.takeDamage(actualDamage);
        logEntry = '${enemy.name} strikes with its wings for $actualDamage damage!';
        break;
        
      case 'intimidate':
        // Dragon intimidate - reduces player attack temporarily
        newPlayer = newPlayer.addTemporaryBuff('attack', -5);
        logEntry = '${enemy.name} roars intimidatingly! Your attack is reduced!';
        break;
        
      default:
        return _enemyAttack(state); // Unknown ability, fallback to attack
    }
    
    var newState = state.copyWith(
      player: newPlayer,
      phase: BattlePhase.playerTurn,
    ).addLogEntry(logEntry);
    
    // Check if player is defeated
    if (!newPlayer.isAlive) {
      newState = newState.copyWith(phase: BattlePhase.defeat);
      newState = newState.addLogEntry('${state.player.name} is defeated!');
    }
    
    return newState;
  }

  static BattleRewards calculateRewards(Enemy enemy) {
    return enemy.lootTable.generateRewards();
  }

  static Character applyVictoryRewards(Character player, BattleRewards rewards) {
    var updatedPlayer = player
        .gainExperience(rewards.experience)
        .addGold(rewards.gold)
        .clearTemporaryBuffs(); // Clear battle buffs
    
    return updatedPlayer;
  }
}
