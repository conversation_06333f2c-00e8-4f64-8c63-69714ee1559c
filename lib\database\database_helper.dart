import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/high_score.dart';
import '../models/game_settings.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'space_invaders.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE high_scores(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        playerName TEXT NOT NULL,
        score INTEGER NOT NULL,
        date INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE game_settings(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        soundEnabled INTEGER NOT NULL,
        musicEnabled INTEGER NOT NULL,
        soundVolume REAL NOT NULL,
        musicVolume REAL NOT NULL,
        controlType TEXT NOT NULL
      )
    ''');

    // Insert default settings
    await db.insert('game_settings', GameSettings().toMap());
  }

  // High Score operations
  Future<int> insertHighScore(HighScore highScore) async {
    final db = await database;
    return await db.insert('high_scores', highScore.toMap());
  }

  Future<List<HighScore>> getHighScores({int limit = 10}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'high_scores',
      orderBy: 'score DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return HighScore.fromMap(maps[i]);
    });
  }

  Future<bool> isHighScore(int score) async {
    final highScores = await getHighScores();
    if (highScores.length < 10) return true;
    return score > highScores.last.score;
  }

  Future<void> deleteHighScore(int id) async {
    final db = await database;
    await db.delete(
      'high_scores',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  Future<GameSettings> getSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('game_settings');
    
    if (maps.isNotEmpty) {
      return GameSettings.fromMap(maps.first);
    } else {
      // If no settings exist, create default ones
      final defaultSettings = GameSettings();
      await insertSettings(defaultSettings);
      return defaultSettings;
    }
  }

  Future<int> insertSettings(GameSettings settings) async {
    final db = await database;
    return await db.insert('game_settings', settings.toMap());
  }

  Future<void> updateSettings(GameSettings settings) async {
    final db = await database;
    await db.update(
      'game_settings',
      settings.toMap(),
      where: 'id = ?',
      whereArgs: [settings.id],
    );
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
