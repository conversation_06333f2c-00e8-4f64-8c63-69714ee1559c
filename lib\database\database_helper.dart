import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:convert';
import '../models/character.dart';
import '../models/character_stats.dart';
import '../models/inventory.dart';
import '../models/item.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'rpg_game.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Characters table
    await db.execute('''
      CREATE TABLE characters(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        level INTEGER NOT NULL,
        experience INTEGER NOT NULL,
        experienceToNext INTEGER NOT NULL,
        maxHp INTEGER NOT NULL,
        currentHp INTEGER NOT NULL,
        attack INTEGER NOT NULL,
        defense INTEGER NOT NULL,
        speed INTEGER NOT NULL,
        gold INTEGER NOT NULL,
        equippedWeapon TEXT,
        equippedArmor TEXT,
        equippedAccessory TEXT,
        createdAt INTEGER NOT NULL,
        lastPlayed INTEGER NOT NULL
      )
    ''');

    // Inventory table
    await db.execute('''
      CREATE TABLE inventory(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        characterId TEXT NOT NULL,
        itemId TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        FOREIGN KEY (characterId) REFERENCES characters (id)
      )
    ''');

    // Game state table
    await db.execute('''
      CREATE TABLE game_state(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        characterId TEXT NOT NULL,
        currentLocation TEXT,
        questProgress TEXT,
        battleCount INTEGER DEFAULT 0,
        lastSaved INTEGER NOT NULL,
        FOREIGN KEY (characterId) REFERENCES characters (id)
      )
    ''');

    // High scores table
    await db.execute('''
      CREATE TABLE high_scores(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        characterName TEXT NOT NULL,
        level INTEGER NOT NULL,
        gold INTEGER NOT NULL,
        battlesWon INTEGER NOT NULL,
        createdAt INTEGER NOT NULL
      )
    ''');
  }

  // Character operations
  Future<String> saveCharacter(Character character) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await db.insert(
      'characters',
      {
        'id': character.id,
        'name': character.name,
        'level': character.baseStats.level,
        'experience': character.baseStats.experience,
        'experienceToNext': character.baseStats.experienceToNext,
        'maxHp': character.baseStats.maxHp,
        'currentHp': character.baseStats.currentHp,
        'attack': character.baseStats.attack,
        'defense': character.baseStats.defense,
        'speed': character.baseStats.speed,
        'gold': character.baseStats.gold,
        'equippedWeapon': character.equipment.weapon?.id,
        'equippedArmor': character.equipment.armor?.id,
        'equippedAccessory': character.equipment.accessory?.id,
        'createdAt': now,
        'lastPlayed': now,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    
    return character.id;
  }

  Future<void> updateCharacter(Character character) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await db.update(
      'characters',
      {
        'name': character.name,
        'level': character.baseStats.level,
        'experience': character.baseStats.experience,
        'experienceToNext': character.baseStats.experienceToNext,
        'maxHp': character.baseStats.maxHp,
        'currentHp': character.baseStats.currentHp,
        'attack': character.baseStats.attack,
        'defense': character.baseStats.defense,
        'speed': character.baseStats.speed,
        'gold': character.baseStats.gold,
        'equippedWeapon': character.equipment.weapon?.id,
        'equippedArmor': character.equipment.armor?.id,
        'equippedAccessory': character.equipment.accessory?.id,
        'lastPlayed': now,
      },
      where: 'id = ?',
      whereArgs: [character.id],
    );
  }

  Future<Character?> loadCharacter(String characterId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'characters',
      where: 'id = ?',
      whereArgs: [characterId],
    );

    if (maps.isEmpty) return null;

    final characterData = maps.first;
    
    // Load equipment
    final equipment = Equipment(
      weapon: characterData['equippedWeapon'] != null 
          ? ItemDatabase.getItemById(characterData['equippedWeapon']) 
          : null,
      armor: characterData['equippedArmor'] != null 
          ? ItemDatabase.getItemById(characterData['equippedArmor']) 
          : null,
      accessory: characterData['equippedAccessory'] != null 
          ? ItemDatabase.getItemById(characterData['equippedAccessory']) 
          : null,
    );

    return Character(
      id: characterData['id'],
      name: characterData['name'],
      baseStats: CharacterStats(
        level: characterData['level'],
        experience: characterData['experience'],
        experienceToNext: characterData['experienceToNext'],
        maxHp: characterData['maxHp'],
        currentHp: characterData['currentHp'],
        attack: characterData['attack'],
        defense: characterData['defense'],
        speed: characterData['speed'],
        gold: characterData['gold'],
      ),
      equipment: equipment,
    );
  }

  Future<List<Character>> getAllCharacters() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'characters',
      orderBy: 'lastPlayed DESC',
    );

    List<Character> characters = [];
    for (final characterData in maps) {
      final equipment = Equipment(
        weapon: characterData['equippedWeapon'] != null 
            ? ItemDatabase.getItemById(characterData['equippedWeapon']) 
            : null,
        armor: characterData['equippedArmor'] != null 
            ? ItemDatabase.getItemById(characterData['equippedArmor']) 
            : null,
        accessory: characterData['equippedAccessory'] != null 
            ? ItemDatabase.getItemById(characterData['equippedAccessory']) 
            : null,
      );

      characters.add(Character(
        id: characterData['id'],
        name: characterData['name'],
        baseStats: CharacterStats(
          level: characterData['level'],
          experience: characterData['experience'],
          experienceToNext: characterData['experienceToNext'],
          maxHp: characterData['maxHp'],
          currentHp: characterData['currentHp'],
          attack: characterData['attack'],
          defense: characterData['defense'],
          speed: characterData['speed'],
          gold: characterData['gold'],
        ),
        equipment: equipment,
      ));
    }

    return characters;
  }

  Future<void> deleteCharacter(String characterId) async {
    final db = await database;
    
    // Delete character and related data
    await db.delete('characters', where: 'id = ?', whereArgs: [characterId]);
    await db.delete('inventory', where: 'characterId = ?', whereArgs: [characterId]);
    await db.delete('game_state', where: 'characterId = ?', whereArgs: [characterId]);
  }

  // Inventory operations
  Future<void> saveInventory(String characterId, Inventory inventory) async {
    final db = await database;
    
    // Clear existing inventory
    await db.delete('inventory', where: 'characterId = ?', whereArgs: [characterId]);
    
    // Save new inventory
    for (final slot in inventory.slots) {
      await db.insert('inventory', {
        'characterId': characterId,
        'itemId': slot.item.id,
        'quantity': slot.quantity,
      });
    }
  }

  Future<Inventory> loadInventory(String characterId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'inventory',
      where: 'characterId = ?',
      whereArgs: [characterId],
    );

    List<InventorySlot> slots = [];
    for (final slotData in maps) {
      final item = ItemDatabase.getItemById(slotData['itemId']);
      if (item != null) {
        slots.add(InventorySlot(
          item: item,
          quantity: slotData['quantity'],
        ));
      }
    }

    return Inventory(slots: slots);
  }

  // High scores
  Future<void> saveHighScore(String characterName, int level, int gold, int battlesWon) async {
    final db = await database;
    await db.insert('high_scores', {
      'characterName': characterName,
      'level': level,
      'gold': gold,
      'battlesWon': battlesWon,
      'createdAt': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<List<Map<String, dynamic>>> getHighScores({int limit = 10}) async {
    final db = await database;
    return await db.query(
      'high_scores',
      orderBy: 'level DESC, gold DESC',
      limit: limit,
    );
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
