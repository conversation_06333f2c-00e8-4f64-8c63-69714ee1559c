import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:convert';
import '../models/business.dart';
import '../models/employee.dart';
import '../models/product.dart';
import '../models/market.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'business_tycoon.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Businesses table
    await db.execute('''
      CREATE TABLE businesses(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        industry TEXT NOT NULL,
        foundedDate INTEGER NOT NULL,
        cash REAL NOT NULL,
        reputation REAL NOT NULL,
        level INTEGER NOT NULL,
        monthlyRent REAL NOT NULL,
        marketingBudget REAL NOT NULL,
        unlockedCities TEXT,
        createdAt INTEGER NOT NULL,
        lastPlayed INTEGER NOT NULL
      )
    ''');

    // Employees table
    await db.execute('''
      CREATE TABLE employees(
        id TEXT PRIMARY KEY,
        businessId TEXT NOT NULL,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        skillLevel TEXT NOT NULL,
        salary REAL NOT NULL,
        hiredDate INTEGER NOT NULL,
        productivity REAL NOT NULL,
        satisfaction REAL NOT NULL,
        experience INTEGER NOT NULL,
        FOREIGN KEY (businessId) REFERENCES businesses (id)
      )
    ''');

    // Products table
    await db.execute('''
      CREATE TABLE products(
        id TEXT PRIMARY KEY,
        businessId TEXT NOT NULL,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        productionCost REAL NOT NULL,
        sellingPrice REAL NOT NULL,
        stock INTEGER NOT NULL,
        quality REAL NOT NULL,
        demand REAL NOT NULL,
        unitsSold INTEGER NOT NULL,
        lastRestocked INTEGER NOT NULL,
        FOREIGN KEY (businessId) REFERENCES businesses (id)
      )
    ''');

    // Market data table
    await db.execute('''
      CREATE TABLE market_data(
        id INTEGER PRIMARY KEY,
        inflationRate REAL NOT NULL,
        condition TEXT NOT NULL,
        industryTrends TEXT NOT NULL,
        lastUpdate INTEGER NOT NULL
      )
    ''');

    // Financial records table
    await db.execute('''
      CREATE TABLE financial_records(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        businessId TEXT NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        date INTEGER NOT NULL,
        FOREIGN KEY (businessId) REFERENCES businesses (id)
      )
    ''');

    // Game statistics table
    await db.execute('''
      CREATE TABLE game_stats(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        businessId TEXT NOT NULL,
        totalRevenue REAL DEFAULT 0,
        totalExpenses REAL DEFAULT 0,
        highestValuation REAL DEFAULT 0,
        daysInBusiness INTEGER DEFAULT 0,
        employeesHired INTEGER DEFAULT 0,
        productsLaunched INTEGER DEFAULT 0,
        FOREIGN KEY (businessId) REFERENCES businesses (id)
      )
    ''');

    // Initialize default market data
    await db.insert('market_data', {
      'inflationRate': 0.02,
      'condition': 'MarketCondition.normal',
      'industryTrends': jsonEncode({
        'technology': 1.0,
        'retail': 1.0,
        'manufacturing': 1.0,
        'food': 1.0,
        'healthcare': 1.0,
      }),
      'lastUpdate': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // Business operations
  Future<String> saveBusiness(Business business) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await db.insert(
      'businesses',
      {
        'id': business.id,
        'name': business.name,
        'industry': business.industry.toString(),
        'foundedDate': business.foundedDate.millisecondsSinceEpoch,
        'cash': business.cash,
        'reputation': business.reputation,
        'level': business.level,
        'monthlyRent': business.monthlyRent,
        'marketingBudget': business.marketingBudget,
        'unlockedCities': business.unlockedCities.join(','),
        'createdAt': now,
        'lastPlayed': now,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    
    return business.id;
  }

  Future<void> updateBusiness(Business business) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await db.update(
      'businesses',
      {
        'name': business.name,
        'cash': business.cash,
        'reputation': business.reputation,
        'level': business.level,
        'monthlyRent': business.monthlyRent,
        'marketingBudget': business.marketingBudget,
        'unlockedCities': business.unlockedCities.join(','),
        'lastPlayed': now,
      },
      where: 'id = ?',
      whereArgs: [business.id],
    );
  }

  Future<Business?> loadBusiness(String businessId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'businesses',
      where: 'id = ?',
      whereArgs: [businessId],
    );

    if (maps.isEmpty) return null;

    final businessData = maps.first;
    final business = Business.fromMap(businessData);
    
    // Load employees
    business.employees.addAll(await loadEmployees(businessId));
    
    // Load products
    business.products.addAll(await loadProducts(businessId));
    
    return business;
  }

  Future<List<Business>> getAllBusinesses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'businesses',
      orderBy: 'lastPlayed DESC',
    );

    List<Business> businesses = [];
    for (final businessData in maps) {
      final business = Business.fromMap(businessData);
      businesses.add(business);
    }

    return businesses;
  }

  Future<void> deleteBusiness(String businessId) async {
    final db = await database;
    
    // Delete business and related data
    await db.delete('businesses', where: 'id = ?', whereArgs: [businessId]);
    await db.delete('employees', where: 'businessId = ?', whereArgs: [businessId]);
    await db.delete('products', where: 'businessId = ?', whereArgs: [businessId]);
    await db.delete('financial_records', where: 'businessId = ?', whereArgs: [businessId]);
    await db.delete('game_stats', where: 'businessId = ?', whereArgs: [businessId]);
  }

  // Employee operations
  Future<void> saveEmployee(String businessId, Employee employee) async {
    final db = await database;
    await db.insert('employees', {
      ...employee.toMap(),
      'businessId': businessId,
    });
  }

  Future<List<Employee>> loadEmployees(String businessId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      where: 'businessId = ?',
      whereArgs: [businessId],
    );

    return maps.map((map) => Employee.fromMap(map)).toList();
  }

  Future<void> updateEmployee(Employee employee) async {
    final db = await database;
    await db.update(
      'employees',
      employee.toMap(),
      where: 'id = ?',
      whereArgs: [employee.id],
    );
  }

  Future<void> deleteEmployee(String employeeId) async {
    final db = await database;
    await db.delete('employees', where: 'id = ?', whereArgs: [employeeId]);
  }

  // Product operations
  Future<void> saveProduct(String businessId, Product product) async {
    final db = await database;
    await db.insert('products', {
      ...product.toMap(),
      'businessId': businessId,
    });
  }

  Future<List<Product>> loadProducts(String businessId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'businessId = ?',
      whereArgs: [businessId],
    );

    return maps.map((map) => Product.fromMap(map)).toList();
  }

  Future<void> updateProduct(Product product) async {
    final db = await database;
    await db.update(
      'products',
      product.toMap(),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  Future<void> deleteProduct(String productId) async {
    final db = await database;
    await db.delete('products', where: 'id = ?', whereArgs: [productId]);
  }

  // Market operations
  Future<Market> loadMarket() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('market_data', limit: 1);
    
    if (maps.isEmpty) {
      return Market(); // Return default market
    }
    
    return Market.fromMap(maps.first);
  }

  Future<void> saveMarket(Market market) async {
    final db = await database;
    await db.update(
      'market_data',
      market.toMap(),
      where: 'id = ?',
      whereArgs: [1],
    );
  }

  // Financial records
  Future<void> addFinancialRecord(String businessId, String type, String category, double amount, String description) async {
    final db = await database;
    await db.insert('financial_records', {
      'businessId': businessId,
      'type': type, // 'revenue' or 'expense'
      'category': category,
      'amount': amount,
      'description': description,
      'date': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<List<Map<String, dynamic>>> getFinancialRecords(String businessId, {int? limit}) async {
    final db = await database;
    return await db.query(
      'financial_records',
      where: 'businessId = ?',
      whereArgs: [businessId],
      orderBy: 'date DESC',
      limit: limit,
    );
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
