import 'dart:ui';
import 'game_object.dart';

class Player extends GameObject {
  double speed;
  int lives;
  bool canShoot;
  double shootCooldown;
  double currentCooldown;

  Player({
    required double x,
    required double y,
    this.speed = 200.0,
    this.lives = 3,
    this.canShoot = true,
    this.shootCooldown = 0.3,
    this.currentCooldown = 0.0,
  }) : super(
          x: x,
          y: y,
          width: 40.0,
          height: 30.0,
        );

  @override
  void update(double deltaTime) {
    // Update shooting cooldown
    if (currentCooldown > 0) {
      currentCooldown -= deltaTime;
      if (currentCooldown <= 0) {
        canShoot = true;
      }
    }
  }

  void moveLeft(double deltaTime, double screenWidth) {
    x -= speed * deltaTime;
    if (x < 0) x = 0;
  }

  void moveRight(double deltaTime, double screenWidth) {
    x += speed * deltaTime;
    if (x + width > screenWidth) x = screenWidth - width;
  }

  bool shoot() {
    if (canShoot) {
      canShoot = false;
      currentCooldown = shootCooldown;
      return true;
    }
    return false;
  }

  void takeDamage() {
    lives--;
    if (lives < 0) lives = 0;
  }

  bool isAlive() => lives > 0;

  @override
  void render(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF00FF00)
      ..style = PaintingStyle.fill;

    // Draw player as a simple triangle/spaceship shape
    final path = Path();
    path.moveTo(x + width / 2, y); // Top point
    path.lineTo(x, y + height); // Bottom left
    path.lineTo(x + width, y + height); // Bottom right
    path.close();

    canvas.drawPath(path, paint);

    // Draw a small rectangle for the cockpit
    final cockpitPaint = Paint()
      ..color = const Color(0xFF0080FF)
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(x + width * 0.4, y + height * 0.3, width * 0.2, height * 0.3),
      cockpitPaint,
    );
  }
}
