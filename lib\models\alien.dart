import 'dart:ui';
import 'dart:math';
import 'game_object.dart';

enum AlienType { basic, fast, strong }

class Alien extends GameObject {
  AlienType type;
  late double speed;
  late int points;
  late double shootProbability;
  double lastShotTime;

  Alien({
    required double x,
    required double y,
    required this.type,
    this.lastShotTime = 0.0,
  }) : super(
          x: x,
          y: y,
          width: _getWidthForType(type),
          height: _getHeightForType(type),
        ) {
    _initializeByType();
  }

  static double _getWidthForType(AlienType type) {
    switch (type) {
      case AlienType.basic:
        return 30.0;
      case AlienType.fast:
        return 25.0;
      case AlienType.strong:
        return 35.0;
    }
  }

  static double _getHeightForType(AlienType type) {
    switch (type) {
      case AlienType.basic:
        return 25.0;
      case AlienType.fast:
        return 20.0;
      case AlienType.strong:
        return 30.0;
    }
  }

  void _initializeByType() {
    switch (type) {
      case AlienType.basic:
        speed = 50.0;
        points = 10;
        shootProbability = 0.001;
        break;
      case AlienType.fast:
        speed = 80.0;
        points = 20;
        shootProbability = 0.002;
        break;
      case AlienType.strong:
        speed = 30.0;
        points = 30;
        shootProbability = 0.0015;
        break;
    }
  }

  @override
  void update(double deltaTime) {
    // Aliens move in formation, movement is handled by the game engine
    lastShotTime += deltaTime;
  }

  void moveDown(double distance) {
    y += distance;
  }

  void moveHorizontal(double deltaTime, double direction) {
    x += speed * deltaTime * direction;
  }

  bool shouldShoot(double currentTime) {
    if (currentTime - lastShotTime > 2.0) { // Minimum 2 seconds between shots
      return (DateTime.now().millisecondsSinceEpoch % 1000) / 1000.0 < shootProbability;
    }
    return false;
  }

  void shoot() {
    lastShotTime = DateTime.now().millisecondsSinceEpoch / 1000.0;
  }

  Color _getColorForType() {
    switch (type) {
      case AlienType.basic:
        return const Color(0xFFFF0000); // Red
      case AlienType.fast:
        return const Color(0xFFFFFF00); // Yellow
      case AlienType.strong:
        return const Color(0xFFFF00FF); // Magenta
    }
  }

  @override
  void render(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = _getColorForType()
      ..style = PaintingStyle.fill;

    // Draw alien as a simple shape based on type
    switch (type) {
      case AlienType.basic:
        // Draw as rectangle with small details
        canvas.drawRect(bounds, paint);

        // Add eyes
        final eyePaint = Paint()..color = const Color(0xFF000000);
        canvas.drawCircle(Offset(x + width * 0.3, y + height * 0.3), 2, eyePaint);
        canvas.drawCircle(Offset(x + width * 0.7, y + height * 0.3), 2, eyePaint);
        break;

      case AlienType.fast:
        // Draw as diamond shape
        final path = Path();
        path.moveTo(x + width / 2, y); // Top
        path.lineTo(x + width, y + height / 2); // Right
        path.lineTo(x + width / 2, y + height); // Bottom
        path.lineTo(x, y + height / 2); // Left
        path.close();
        canvas.drawPath(path, paint);
        break;

      case AlienType.strong:
        // Draw as hexagon
        final path = Path();
        final centerX = x + width / 2;
        final centerY = y + height / 2;
        final radius = width / 2;

        for (int i = 0; i < 6; i++) {
          final angle = (i * 60) * (3.14159 / 180);
          final pointX = centerX + radius * cos(angle);
          final pointY = centerY + radius * sin(angle);

          if (i == 0) {
            path.moveTo(pointX, pointY);
          } else {
            path.lineTo(pointX, pointY);
          }
        }
        path.close();
        canvas.drawPath(path, paint);
        break;
    }
  }
}


