import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/game_canvas.dart';
import '../widgets/hud_widget.dart';
import '../widgets/control_buttons.dart';
import '../models/game_state.dart';
import 'game_over_screen.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 16), // ~60 FPS
      vsync: this,
    );
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGame();
    });
  }

  void _initializeGame() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final size = MediaQuery.of(context).size;
    
    gameProvider.initializeGame(size.width, size.height - 100); // Account for HUD
    gameProvider.startNewGame();
    
    setState(() {
      _isInitialized = true;
    });
    
    // Start animation loop for smooth rendering
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Consumer2<GameProvider, SettingsProvider>(
          builder: (context, gameProvider, settingsProvider, child) {
            if (!_isInitialized || gameProvider.gameState == null) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.cyan),
              );
            }

            // Check for game over
            if (gameProvider.gameState!.status == GameStatus.gameOver) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => GameOverScreen(
                      score: gameProvider.score,
                      level: gameProvider.level,
                    ),
                  ),
                );
              });
            }

            return Stack(
              children: [
                // Game Canvas
                Positioned.fill(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return GameCanvas(
                        gameState: gameProvider.gameState!,
                        screenSize: Size(
                          MediaQuery.of(context).size.width,
                          MediaQuery.of(context).size.height - 100,
                        ),
                      );
                    },
                  ),
                ),

                // HUD
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.8),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: HudWidget(
                      score: gameProvider.score,
                      lives: gameProvider.lives,
                      level: gameProvider.level,
                    ),
                  ),
                ),

                // Touch Controls or Buttons
                if (settingsProvider.controlType == 'buttons')
                  Positioned.fill(
                    child: ControlButtons(
                      onMoveLeft: () => gameProvider.movePlayerLeft(),
                      onMoveRight: () => gameProvider.movePlayerRight(),
                      onShoot: () => gameProvider.playerShoot(),
                      onPause: _togglePause,
                      showButtons: true,
                    ),
                  )
                else
                  _buildTouchControls(gameProvider),

                // Pause button for touch controls
                if (settingsProvider.controlType == 'touch')
                  Positioned(
                    top: 90,
                    right: 16,
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: Colors.orange.withOpacity(0.7),
                      onPressed: _togglePause,
                      child: Icon(
                        gameProvider.isPaused ? Icons.play_arrow : Icons.pause,
                        color: Colors.white,
                      ),
                    ),
                  ),

                // Level Complete Dialog
                if (gameProvider.gameState!.status == GameStatus.levelComplete)
                  _buildLevelCompleteDialog(gameProvider),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildTouchControls(GameProvider gameProvider) {
    return GestureDetector(
      onTap: () => gameProvider.playerShoot(),
      onPanUpdate: (details) {
        final screenWidth = MediaQuery.of(context).size.width;
        final delta = details.delta.dx;
        
        if (delta > 0) {
          gameProvider.movePlayerRight();
        } else if (delta < 0) {
          gameProvider.movePlayerLeft();
        }
      },
      child: Container(
        color: Colors.transparent,
        child: const Center(
          child: Padding(
            padding: EdgeInsets.only(bottom: 100),
            child: Text(
              'Swipe to move • Tap to shoot',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLevelCompleteDialog(GameProvider gameProvider) {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.cyan, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'LEVEL COMPLETE!',
                style: TextStyle(
                  color: Colors.cyan,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Level ${gameProvider.level} cleared!',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Score: ${gameProvider.score}',
                style: const TextStyle(
                  color: Colors.green,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => gameProvider.nextLevel(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan,
                  foregroundColor: Colors.black,
                ),
                child: const Text('NEXT LEVEL'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _togglePause() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    if (gameProvider.isPaused) {
      gameProvider.resumeGame();
    } else {
      gameProvider.pauseGame();
    }
  }
}
