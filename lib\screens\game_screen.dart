import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../models/enemy.dart';
import 'character_screen.dart';
import 'inventory_screen.dart';
import 'battle_screen.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Adventure'),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsMenu(context),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: Consumer<GameProvider>(
          builder: (context, gameProvider, child) {
            final character = gameProvider.currentCharacter;
            
            if (character == null) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            // Check if in battle
            if (gameProvider.isInBattle) {
              return const BattleScreen();
            }

            return _buildMainGameView(character, gameProvider);
          },
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildMainGameView(character, GameProvider gameProvider) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Character Status Card
            _buildCharacterStatusCard(character),
            
            const SizedBox(height: 20),
            
            // Action Buttons
            Expanded(
              child: _buildActionGrid(gameProvider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterStatusCard(character) {
    final hpPercentage = character.baseStats.currentHp / character.totalStats.maxHp;
    
    return Card(
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                // Character Avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.amber, width: 3),
                    gradient: const LinearGradient(
                      colors: [Colors.amber, Colors.orange],
                    ),
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 30,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Character Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        character.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Level ${character.baseStats.level}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.amber,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${character.baseStats.gold} Gold',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Health Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Health',
                      style: TextStyle(
                        color: Colors.white70,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${character.baseStats.currentHp}/${character.totalStats.maxHp}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: hpPercentage,
                  backgroundColor: Colors.red.withOpacity(0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    hpPercentage > 0.6 ? Colors.green :
                    hpPercentage > 0.3 ? Colors.orange : Colors.red,
                  ),
                  minHeight: 8,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Experience Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Experience',
                      style: TextStyle(
                        color: Colors.white70,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${character.baseStats.experience}/${character.baseStats.experienceToNext}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: character.baseStats.experience / character.baseStats.experienceToNext,
                  backgroundColor: Colors.blue.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 8,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionGrid(GameProvider gameProvider) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildActionCard(
          'Explore',
          'Find enemies and treasures',
          Icons.explore,
          Colors.green,
          () => _startRandomBattle(gameProvider),
        ),
        _buildActionCard(
          'Rest',
          'Restore health to full',
          Icons.hotel,
          Colors.blue,
          () => _restCharacter(gameProvider),
        ),
        _buildActionCard(
          'Shop',
          'Buy and sell items',
          Icons.store,
          Colors.purple,
          () => _showShopDialog(),
        ),
        _buildActionCard(
          'Training',
          'Practice combat skills',
          Icons.fitness_center,
          Colors.orange,
          () => _showTrainingDialog(),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: color.withOpacity(0.2),
                  border: Border.all(color: color, width: 2),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          backgroundColor: Theme.of(context).cardColor,
          selectedItemColor: Colors.amber,
          unselectedItemColor: Colors.white54,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.person),
              label: 'Character',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.backpack),
              label: 'Inventory',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'Adventure',
            ),
          ],
          currentIndex: 2, // Adventure tab is selected
          onTap: (index) {
            switch (index) {
              case 0:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CharacterScreen(),
                  ),
                );
                break;
              case 1:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const InventoryScreen(),
                  ),
                );
                break;
              case 2:
                // Already on adventure screen
                break;
            }
          },
        );
      },
    );
  }

  void _startRandomBattle(GameProvider gameProvider) {
    gameProvider.startRandomBattle();
  }

  void _restCharacter(GameProvider gameProvider) {
    gameProvider.restoreCharacterHealth();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('You feel refreshed! Health restored to full.'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showShopDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Shop'),
        content: const Text('Shop feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTrainingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: const Text('Training'),
        content: const Text('Training feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSettingsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).cardColor,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.save, color: Colors.green),
              title: const Text('Save Game'),
              onTap: () {
                Navigator.pop(context);
                context.read<GameProvider>().saveCurrentCharacter();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Game saved!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.home, color: Colors.blue),
              title: const Text('Main Menu'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/',
                  (route) => false,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
