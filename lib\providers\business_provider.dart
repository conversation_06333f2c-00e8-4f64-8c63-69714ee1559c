import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import '../models/business.dart';
import '../models/employee.dart';
import '../models/product.dart';
import '../models/market.dart';
import '../database/database_helper.dart';

class BusinessProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Business? _currentBusiness;
  Market _market = Market();
  List<Business> _savedBusinesses = [];
  bool _isLoading = false;
  Timer? _gameTimer;

  // Getters
  Business? get currentBusiness => _currentBusiness;
  Market get market => _market;
  List<Business> get savedBusinesses => _savedBusinesses;
  bool get isLoading => _isLoading;

  // Game time simulation
  DateTime _gameTime = DateTime.now();
  DateTime get gameTime => _gameTime;

  // Business management
  Future<void> createNewBusiness(String name, Industry industry) async {
    _isLoading = true;
    notifyListeners();

    try {
      final businessId = _uuid.v4();
      final business = Business(
        id: businessId,
        name: name,
        industry: industry,
        foundedDate: DateTime.now(),
      );

      // Add starter products based on industry
      business.products.addAll(_getStarterProducts(industry));

      await _databaseHelper.saveBusiness(business);
      
      // Save starter products
      for (final product in business.products) {
        await _databaseHelper.saveProduct(businessId, product);
      }

      _currentBusiness = business;
      await loadSavedBusinesses();
      _startGameTimer();
    } catch (e) {
      debugPrint('Error creating business: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  List<Product> _getStarterProducts(Industry industry) {
    switch (industry) {
      case Industry.technology:
        return ProductTemplates.getTechnologyProducts().take(1).toList();
      case Industry.retail:
        return ProductTemplates.getRetailProducts().take(2).toList();
      case Industry.manufacturing:
        return ProductTemplates.getManufacturingProducts().take(1).toList();
      case Industry.food:
        return ProductTemplates.getFoodProducts().take(2).toList();
      case Industry.healthcare:
        return []; // Will be added later
    }
  }

  Future<void> loadBusiness(String businessId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final business = await _databaseHelper.loadBusiness(businessId);
      if (business != null) {
        _currentBusiness = business;
        _startGameTimer();
      }
    } catch (e) {
      debugPrint('Error loading business: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> saveCurrentBusiness() async {
    if (_currentBusiness == null) return;

    try {
      await _databaseHelper.updateBusiness(_currentBusiness!);
      
      // Save employees
      for (final employee in _currentBusiness!.employees) {
        await _databaseHelper.updateEmployee(employee);
      }
      
      // Save products
      for (final product in _currentBusiness!.products) {
        await _databaseHelper.updateProduct(product);
      }
    } catch (e) {
      debugPrint('Error saving business: $e');
    }
  }

  Future<void> loadSavedBusinesses() async {
    try {
      _savedBusinesses = await _databaseHelper.getAllBusinesses();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading saved businesses: $e');
    }
  }

  Future<void> deleteBusiness(String businessId) async {
    try {
      await _databaseHelper.deleteBusiness(businessId);
      await loadSavedBusinesses();
      
      if (_currentBusiness?.id == businessId) {
        _currentBusiness = null;
        _stopGameTimer();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting business: $e');
    }
  }

  // Employee management
  Future<void> hireEmployee(Employee employee) async {
    if (_currentBusiness == null) return;

    if (_currentBusiness!.cash >= employee.salary * 3) { // 3 months salary upfront
      _currentBusiness!.cash -= employee.salary * 3;
      _currentBusiness!.addEmployee(employee);
      
      await _databaseHelper.saveEmployee(_currentBusiness!.id, employee);
      await _databaseHelper.addFinancialRecord(
        _currentBusiness!.id,
        'expense',
        'hiring',
        employee.salary * 3,
        'Hired ${employee.name} as ${employee.fullTitle}',
      );
      
      notifyListeners();
      saveCurrentBusiness();
    }
  }

  Future<void> fireEmployee(String employeeId) async {
    if (_currentBusiness == null) return;

    _currentBusiness!.removeEmployee(employeeId);
    await _databaseHelper.deleteEmployee(employeeId);
    
    notifyListeners();
    saveCurrentBusiness();
  }

  // Product management
  Future<void> launchProduct(Product product) async {
    if (_currentBusiness == null) return;

    _currentBusiness!.addProduct(product);
    await _databaseHelper.saveProduct(_currentBusiness!.id, product);
    
    notifyListeners();
    saveCurrentBusiness();
  }

  Future<void> restockProduct(String productId, int quantity) async {
    if (_currentBusiness == null) return;

    final product = _currentBusiness!.products.firstWhere((p) => p.id == productId);
    final totalCost = product.productionCost * quantity;
    
    if (_currentBusiness!.cash >= totalCost) {
      _currentBusiness!.cash -= totalCost;
      product.restock(quantity, totalCost);
      
      await _databaseHelper.addFinancialRecord(
        _currentBusiness!.id,
        'expense',
        'inventory',
        totalCost,
        'Restocked ${product.name} x$quantity',
      );
      
      notifyListeners();
      saveCurrentBusiness();
    }
  }

  Future<void> adjustProductPrice(String productId, double newPrice) async {
    if (_currentBusiness == null) return;

    final product = _currentBusiness!.products.firstWhere((p) => p.id == productId);
    product.adjustPrice(newPrice);
    
    notifyListeners();
    saveCurrentBusiness();
  }

  // Market operations
  Future<void> loadMarket() async {
    try {
      _market = await _databaseHelper.loadMarket();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading market: $e');
    }
  }

  // Game simulation
  void _startGameTimer() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _simulateGameTick();
    });
  }

  void _stopGameTimer() {
    _gameTimer?.cancel();
  }

  void _simulateGameTick() {
    if (_currentBusiness == null) return;

    // Advance game time
    _gameTime = _gameTime.add(const Duration(hours: 1));

    // Update market
    _market.updateMarket();

    // Simulate sales
    _simulateSales();

    // Process daily operations
    if (_gameTime.hour == 0) {
      _processDailyOperations();
    }

    // Process monthly operations
    if (_gameTime.day == 1 && _gameTime.hour == 0) {
      _processMonthlyOperations();
    }

    notifyListeners();
    saveCurrentBusiness();
  }

  void _simulateSales() {
    if (_currentBusiness == null) return;

    final salesForce = _currentBusiness!.employees
        .where((e) => e.role == EmployeeRole.sales)
        .fold(0.0, (sum, e) => sum + e.effectiveProductivity);

    for (final product in _currentBusiness!.products) {
      if (product.stock > 0) {
        final industryMultiplier = _market.getIndustryMultiplier(_currentBusiness!.industry.toString());
        final marketMultiplier = _market.demandMultiplier;
        final totalMultiplier = industryMultiplier * marketMultiplier * _currentBusiness!.industryBonus;
        
        final salesCount = product.simulateSales(salesForce.round(), totalMultiplier);
        
        if (salesCount > 0) {
          final revenue = salesCount * product.sellingPrice;
          product.sell(salesCount);
          _currentBusiness!.addRevenue('sales', revenue);
          
          _databaseHelper.addFinancialRecord(
            _currentBusiness!.id,
            'revenue',
            'sales',
            revenue,
            'Sold ${product.name} x$salesCount',
          );
        }
      }
    }
  }

  void _processDailyOperations() {
    if (_currentBusiness == null) return;

    // Employee satisfaction and productivity updates
    for (final employee in _currentBusiness!.employees) {
      employee.gainExperience(1);
      
      // Random satisfaction changes
      final satisfactionChange = (DateTime.now().millisecond % 10 - 5) * 0.5;
      employee.updateSatisfaction(satisfactionChange);
    }
  }

  void _processMonthlyOperations() {
    if (_currentBusiness == null) return;

    // Pay monthly costs
    _currentBusiness!.payMonthlyCosts();

    // Check for level up
    if (_currentBusiness!.canLevelUp()) {
      _currentBusiness!.levelUp();
    }

    // Reset monthly stats
    _currentBusiness!.resetMonthlyStats();
  }

  // Financial operations
  Future<void> takeOutLoan(double amount) async {
    if (_currentBusiness == null) return;

    _currentBusiness!.cash += amount;
    _currentBusiness!.addExpense('loan', amount * 0.1); // 10% interest
    
    await _databaseHelper.addFinancialRecord(
      _currentBusiness!.id,
      'revenue',
      'loan',
      amount,
      'Bank loan taken',
    );
    
    notifyListeners();
    saveCurrentBusiness();
  }

  Future<void> investInMarketing(double amount) async {
    if (_currentBusiness == null) return;

    if (_currentBusiness!.cash >= amount) {
      _currentBusiness!.cash -= amount;
      _currentBusiness!.marketingBudget += amount;
      _currentBusiness!.reputation += amount / 1000; // Reputation boost
      
      await _databaseHelper.addFinancialRecord(
        _currentBusiness!.id,
        'expense',
        'marketing',
        amount,
        'Marketing investment',
      );
      
      notifyListeners();
      saveCurrentBusiness();
    }
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _databaseHelper.close();
    super.dispose();
  }
}
