import 'package:flutter/material.dart';
import '../models/game_state.dart';
import '../models/player.dart';
import '../models/alien.dart';
import '../models/bullet.dart';

class GameCanvas extends StatelessWidget {
  final GameState gameState;
  final Size screenSize;

  const GameCanvas({
    Key? key,
    required this.gameState,
    required this.screenSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: screenSize,
      painter: GamePainter(gameState: gameState),
    );
  }
}

class GamePainter extends CustomPainter {
  final GameState gameState;

  GamePainter({required this.gameState});

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background
    _drawBackground(canvas, size);

    // Draw game objects only if game is active
    if (gameState.status == GameStatus.playing || gameState.status == GameStatus.paused) {
      // Draw player
      gameState.player.render(canvas, size);

      // Draw aliens
      for (final alien in gameState.aliens) {
        alien.render(canvas, size);
      }

      // Draw player bullets
      for (final bullet in gameState.playerBullets) {
        bullet.render(canvas, size);
      }

      // Draw alien bullets
      for (final bullet in gameState.alienBullets) {
        bullet.render(canvas, size);
      }

      // Draw pause overlay if paused
      if (gameState.status == GameStatus.paused) {
        _drawPauseOverlay(canvas, size);
      }
    }
  }

  void _drawBackground(Canvas canvas, Size size) {
    // Draw space background
    final backgroundPaint = Paint()
      ..color = const Color(0xFF000011)
      ..style = PaintingStyle.fill;

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // Draw stars
    final starPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Simple star pattern
    for (int i = 0; i < 50; i++) {
      double x = (i * 37) % size.width;
      double y = (i * 23) % size.height;
      double starSize = (i % 3) + 1;
      
      canvas.drawCircle(Offset(x, y), starSize, starPaint);
    }
  }

  void _drawPauseOverlay(Canvas canvas, Size size) {
    // Semi-transparent overlay
    final overlayPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), overlayPaint);

    // Pause text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'PAUSED',
        style: TextStyle(
          color: Colors.white,
          fontSize: 48,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true; // Always repaint for smooth animation
  }
}
