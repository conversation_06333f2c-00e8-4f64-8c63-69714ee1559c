{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\mytodo\\space_invaders_game\\android\\app\\.cxx\\Debug\\6t2r3q46\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\mytodo\\space_invaders_game\\android\\app\\.cxx\\Debug\\6t2r3q46\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}