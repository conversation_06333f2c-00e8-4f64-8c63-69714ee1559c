import 'dart:ui';
import 'game_object.dart';

enum BulletType { player, alien }

class Bullet extends GameObject {
  BulletType type;
  late double speed;
  late double direction; // 1 for down, -1 for up

  Bullet({
    required double x,
    required double y,
    required this.type,
  }) : super(
          x: x,
          y: y,
          width: type == BulletType.player ? 3.0 : 4.0,
          height: type == BulletType.player ? 8.0 : 6.0,
        ) {
    switch (type) {
      case BulletType.player:
        speed = 300.0;
        direction = -1; // Move up
        break;
      case BulletType.alien:
        speed = 150.0;
        direction = 1; // Move down
        break;
    }
  }

  @override
  void update(double deltaTime) {
    y += speed * direction * deltaTime;
  }

  @override
  bool isOnScreen(GameSize screenSize) {
    return y + height >= 0 && y <= screenSize.height;
  }

  Color _getColorForType() {
    switch (type) {
      case BulletType.player:
        return const Color(0xFF00FFFF); // Cyan
      case BulletType.alien:
        return const Color(0xFFFF4444); // Red
    }
  }

  @override
  void render(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = _getColorForType()
      ..style = PaintingStyle.fill;

    if (type == BulletType.player) {
      // Draw player bullet as a thin rectangle
      canvas.drawRect(bounds, paint);
    } else {
      // Draw alien bullet as a circle
      canvas.drawCircle(
        Offset(x + width / 2, y + height / 2),
        width / 2,
        paint,
      );
    }
  }
}
